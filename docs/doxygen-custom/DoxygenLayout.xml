<?xml version="1.0" encoding="UTF-8"?>
<doxygenlayout version="2.0">
  <!-- Generated by doxygen 1.13.2 -->
  <!-- Navigation index tabs for HTML output -->
  <navindex>
    <tab type="mainpage" visible="yes" title=""/>
    <tab type="pages" visible="yes" title="" intro=""/>
    <tab type="topics" visible="yes" title="API Documentation" intro=""/>
    <!-- <tab type="modules" visible="yes" title="" intro="">
      <tab type="modulelist" visible="yes" title="" intro=""/>
      <tab type="modulemembers" visible="yes" title="" intro=""/>
    </tab>
    <tab type="namespaces" visible="yes" title="">
      <tab type="namespacelist" visible="yes" title="" intro=""/>
      <tab type="namespacemembers" visible="yes" title="" intro=""/>
    </tab>
    <tab type="concepts" visible="yes" title="">
    </tab> -->
    <!-- <tab type="interfaces" visible="yes" title="">
      <tab type="interfacelist" visible="yes" title="" intro=""/>
      <tab type="interfaceindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="interfacehierarchy" visible="yes" title="" intro=""/>
    </tab>
    <tab type="classes" visible="yes" title="">
      <tab type="classlist" visible="yes" title="" intro=""/>
      <tab type="classindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="hierarchy" visible="yes" title="" intro=""/>
      <tab type="classmembers" visible="yes" title="" intro=""/>
    </tab>
    <tab type="structs" visible="yes" title="">
      <tab type="structlist" visible="yes" title="" intro=""/>
      <tab type="structindex" visible="$ALPHABETICAL_INDEX" title=""/>
    </tab>
    <tab type="exceptions" visible="yes" title="">
      <tab type="exceptionlist" visible="yes" title="" intro=""/>
      <tab type="exceptionindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="exceptionhierarchy" visible="yes" title="" intro=""/>
    </tab> -->
    <!-- <tab type="files" visible="yes" title="">
      <tab type="filelist" visible="yes" title="" intro=""/>
      <tab type="globals" visible="yes" title="" intro=""/>
    </tab>
    <tab type="examples" visible="yes" title="" intro=""/> -->
  </navindex>

  <!-- Layout definition for a class page -->
  <class>
    <briefdescription visible="yes"/>
    <includes visible="$SHOW_HEADERFILE"/>
    <inheritancegraph visible="yes"/>
    <collaborationgraph visible="yes"/>
    <memberdecl>
      <nestedclasses visible="yes" title=""/>
      <publictypes visible="yes" title=""/>
      <services visible="yes" title=""/>
      <interfaces visible="yes" title=""/>
      <publicslots visible="yes" title=""/>
      <signals visible="yes" title=""/>
      <publicmethods visible="yes" title=""/>
      <publicstaticmethods visible="yes" title=""/>
      <publicattributes visible="yes" title=""/>
      <publicstaticattributes visible="yes" title=""/>
      <protectedtypes visible="yes" title=""/>
      <protectedslots visible="yes" title=""/>
      <protectedmethods visible="yes" title=""/>
      <protectedstaticmethods visible="yes" title=""/>
      <protectedattributes visible="yes" title=""/>
      <protectedstaticattributes visible="yes" title=""/>
      <packagetypes visible="yes" title=""/>
      <packagemethods visible="yes" title=""/>
      <packagestaticmethods visible="yes" title=""/>
      <packageattributes visible="yes" title=""/>
      <packagestaticattributes visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <events visible="yes" title=""/>
      <privatetypes visible="yes" title=""/>
      <privateslots visible="yes" title=""/>
      <privatemethods visible="yes" title=""/>
      <privatestaticmethods visible="yes" title=""/>
      <privateattributes visible="yes" title=""/>
      <privatestaticattributes visible="yes" title=""/>
      <friends visible="yes" title=""/>
      <related visible="yes" title="" subtitle=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
    <memberdef>
      <inlineclasses visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <services visible="yes" title=""/>
      <interfaces visible="yes" title=""/>
      <constructors visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <related visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <events visible="yes" title=""/>
    </memberdef>
    <allmemberslink visible="yes"/>
    <usedfiles visible="$SHOW_USED_FILES"/>
    <authorsection visible="yes"/>
  </class>

  <!-- Layout definition for a namespace page -->
  <namespace>
    <briefdescription visible="yes"/>
    <memberdecl>
      <nestednamespaces visible="yes" title=""/>
      <constantgroups visible="yes" title=""/>
      <interfaces visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <concepts visible="yes" title=""/>
      <structs visible="yes" title=""/>
      <exceptions visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <membergroups visible="yes" visible="yes"/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
    <memberdef>
      <inlineclasses visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <properties visible="yes" title=""/>
    </memberdef>
    <authorsection visible="yes"/>
  </namespace>

  <!-- Layout definition for a concept page -->
  <concept>
    <briefdescription visible="yes"/>
    <includes visible="$SHOW_HEADERFILE"/>
    <definition visible="yes" title=""/>
    <detaileddescription visible="yes" title=""/>
    <authorsection visible="yes"/>
  </concept>

  <!-- Layout definition for a file page -->
  <file>
    <briefdescription visible="yes"/>
    <includes visible="$SHOW_INCLUDE_FILES"/>
    <includegraph visible="yes"/>
    <includedbygraph visible="yes"/>
    <sourcelink visible="yes"/>
    <memberdecl>
      <interfaces visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <structs visible="yes" title=""/>
      <exceptions visible="yes" title=""/>
      <namespaces visible="yes" title=""/>
      <concepts visible="yes" title=""/>
      <constantgroups visible="yes" title=""/>
      <defines visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <membergroups visible="yes" visible="yes"/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
    <memberdef>
      <inlineclasses visible="yes" title=""/>
      <defines visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <properties visible="yes" title=""/>
    </memberdef>
    <authorsection/>
  </file>

  <!-- Layout definition for a group page -->
  <group>
    <briefdescription visible="yes"/>
    <groupgraph visible="yes"/>
    <memberdecl>
      <nestedgroups visible="yes" title=""/>
      <modules visible="yes" title=""/>
      <dirs visible="yes" title=""/>
      <files visible="yes" title=""/>
      <namespaces visible="yes" title=""/>
      <concepts visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <defines visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <enumvalues visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <signals visible="yes" title=""/>
      <publicslots visible="yes" title=""/>
      <protectedslots visible="yes" title=""/>
      <privateslots visible="yes" title=""/>
      <events visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <friends visible="yes" title=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
    <memberdef>
      <pagedocs/>
      <inlineclasses visible="yes" title=""/>
      <defines visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <sequences visible="yes" title=""/>
      <dictionaries visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <enumvalues visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <signals visible="yes" title=""/>
      <publicslots visible="yes" title=""/>
      <protectedslots visible="yes" title=""/>
      <privateslots visible="yes" title=""/>
      <events visible="yes" title=""/>
      <properties visible="yes" title=""/>
      <friends visible="yes" title=""/>
    </memberdef>
    <authorsection visible="yes"/>
  </group>

  <!-- Layout definition for a C++20 module page -->
  <module>
    <briefdescription visible="yes"/>
    <exportedmodules visible="yes"/>
    <memberdecl>
      <concepts visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <enums visible="yes" title=""/>
      <typedefs visible="yes" title=""/>
      <functions visible="yes" title=""/>
      <variables visible="yes" title=""/>
      <membergroups visible="yes" title=""/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
    <memberdecl>
      <files visible="yes"/>
    </memberdecl>
  </module>

  <!-- Layout definition for a directory page -->
  <directory>
    <briefdescription visible="yes"/>
    <directorygraph visible="yes"/>
    <memberdecl>
      <dirs visible="yes"/>
      <files visible="yes"/>
    </memberdecl>
    <detaileddescription visible="yes" title=""/>
  </directory>
</doxygenlayout>
