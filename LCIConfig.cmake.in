if (NOT TARGET LCI::lct)

    @PACKAGE_INIT@

    LIST(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/LCI")

    set_and_check(LCT_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
    set_and_check(LCT_SHARED_LIBRARY "@PACKAGE_CMAKE_INSTALL_LIBDIR@/@CMAKE_SHARED_LIBRARY_PREFIX@lct@CMAKE_SHARED_LIBRARY_SUFFIX@")

    include(CMakeFindDependencyMacro)
    set(CMAKE_THREAD_PREFER_PTHREAD TRUE)
    set(THREADS_PREFER_PTHREAD_FLAG TRUE)
    # find_dependency(Threads)

    add_library(LCI::lct SHARED IMPORTED GLOBAL)
    add_library(LCI::LCT ALIAS LCI::lct)
    set_target_properties(LCI::lct PROPERTIES
        IMPORTED_LOCATION ${LCT_SHARED_LIBRARY}
    )
    target_include_directories(LCI::lct INTERFACE ${LCT_INCLUDE_DIRS})
    # target_link_libraries(LCI::lct INTERFACE Threads::Threads)

    check_required_components(LCT)

    set(LCI_WITH_LCT_ONLY @LCI_WITH_LCT_ONLY@)
    if (NOT LCI_WITH_LCT_ONLY)
        set_and_check(LCI_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
        set_and_check(LCI_SHARED_LIBRARY "@PACKAGE_CMAKE_INSTALL_LIBDIR@/@CMAKE_SHARED_LIBRARY_PREFIX@lci@CMAKE_SHARED_LIBRARY_SUFFIX@")
        # set_and_check(LCI_STATIC_LIBRARY "@PACKAGE_CMAKE_INSTALL_LIBDIR@/@CMAKE_STATIC_LIBRARY_PREFIX@lci@CMAKE_STATIC_LIBRARY_SUFFIX@")

        add_library(LCI::lci SHARED IMPORTED GLOBAL)
        add_library(LCI::LCI ALIAS LCI::lci)
        set_target_properties(LCI::lci PROPERTIES
          IMPORTED_LOCATION ${LCI_SHARED_LIBRARY}
        )
        target_include_directories(LCI::lci INTERFACE ${LCI_INCLUDE_DIRS})

        # set(LCI_NETWORK_BACKENDS_ENABLED "@LCI_NETWORK_BACKENDS_ENABLED@")
        # foreach(BACKEND IN LISTS LCI_NETWORK_BACKENDS_ENABLED)
        #     find_dependency(${BACKEND})
        #     if(BACKEND STREQUAL UCX)
        #         target_link_libraries(LCI::lci INTERFACE ucx::ucp)
        #     else()
        #         target_link_libraries(LCI::lci INTERFACE ${BACKEND}::${BACKEND})
        #     endif()
        # endforeach()
        # target_link_libraries(LCI::lci INTERFACE Threads::Threads LCI::lct)

        check_required_components(LCI)
    endif()
endif()