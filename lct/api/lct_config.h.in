#ifndef LCI_LCT_CONFIG_H
#define LCI_LCT_CONFIG_H

#cmakedefine LCT_DEBUG
#define LCT_CACHE_LINE @LCT_CACHE_LINE@
#cmakedefine LCT_CONFIG_USE_ALIGNED_ALLOC
#define LCT_PMI_BACKEND_DEFAULT "@LCT_PMI_BACKEND_DEFAULT@"
#cmakedefine LCT_PMI_BACKEND_ENABLE_PMI1
#cmakedefine LCT_PMI_BACKEND_ENABLE_PMI2
#cmakedefine LCT_PMI_BACKEND_ENABLE_MPI
#cmakedefine LCT_PMI_BACKEND_ENABLE_PMIX

#endif  // LCI_LCT_CONFIG_H
