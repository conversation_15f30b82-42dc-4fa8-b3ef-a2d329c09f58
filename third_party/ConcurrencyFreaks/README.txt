ConcurrencyFreaks
=================

A library of concurrent data structures and synchronization mechanisms.
More info on http://concurrencyfreaks.blogspot.com/

C11 - Some locks implemented in the C11 language
CPP - Some locks and data structures implemented in C++1x, mostly lock-free and wait-free queues. You should use a compiler that supports C++14 (like gcc 4.9.1) because some classes use std:shared_timed_mutex
D - Some data structures implemented in the D programming language
Java - Synchronization mechanisms and data structures (mostly lock-free and wait-free queues) implemented in Java. Some of them need Java 8 or above.

