# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

#
# Copyright (C) Mellanox Technologies Ltd. 2001-2014.  ALL RIGHTS RESERVED.
# Copyright (C) UT-Battelle, LLC. 2014-2017. ALL RIGHTS RESERVED.
# Copyright (C) ARM Ltd. 2016-2017.  ALL RIGHTS RESERVED.
# See file LICENSE for terms.
#

#
# Copyright (C) 2021 Nvidia Corporation. All Rights Reserved.
# See file LICENSE for terms.
#




VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
bin_PROGRAMS = $(am__EXEEXT_1)
@HAVE_AARCH64_THUNDERX2_TRUE@am__append_1 = \
@HAVE_AARCH64_THUNDERX2_TRUE@        arch/aarch64/memcpy_thunderx2.S

@HAVE_STATS_TRUE@am__append_2 = \
@HAVE_STATS_TRUE@	stats/client_server.c \
@HAVE_STATS_TRUE@	stats/serialization.c \
@HAVE_STATS_TRUE@	stats/libstats.c

@HAVE_STATS_TRUE@am__append_3 = ucs_stats_parser
subdir = src/ucs
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/config/m4/gtest.m4 \
	$(top_srcdir)/config/m4/libtool.m4 \
	$(top_srcdir)/config/m4/ltoptions.m4 \
	$(top_srcdir)/config/m4/ltsugar.m4 \
	$(top_srcdir)/config/m4/ltversion.m4 \
	$(top_srcdir)/config/m4/lt~obsolete.m4 \
	$(top_srcdir)/config/m4/ax_prog_doxygen.m4 \
	$(top_srcdir)/config/m4/graphviz.m4 \
	$(top_srcdir)/config/m4/ucg.m4 \
	$(top_srcdir)/config/m4/compiler.m4 \
	$(top_srcdir)/config/m4/sysdep.m4 \
	$(top_srcdir)/config/m4/ucm.m4 $(top_srcdir)/config/m4/mpi.m4 \
	$(top_srcdir)/config/m4/rte.m4 \
	$(top_srcdir)/config/m4/fuse3.m4 $(top_srcdir)/config/m4/go.m4 \
	$(top_srcdir)/config/m4/java.m4 \
	$(top_srcdir)/config/m4/cuda.m4 \
	$(top_srcdir)/config/m4/rocm.m4 \
	$(top_srcdir)/config/m4/gdrcopy.m4 \
	$(top_srcdir)/src/ucm/configure.m4 \
	$(top_srcdir)/src/ucm/cuda/configure.m4 \
	$(top_srcdir)/src/ucm/rocm/configure.m4 \
	$(top_srcdir)/src/ucs/configure.m4 \
	$(top_srcdir)/src/ucs/vfs/sock/configure.m4 \
	$(top_srcdir)/src/ucs/vfs/fuse/configure.m4 \
	$(top_srcdir)/src/uct/configure.m4 \
	$(top_srcdir)/src/uct/cuda/configure.m4 \
	$(top_srcdir)/src/uct/cuda/gdr_copy/configure.m4 \
	$(top_srcdir)/src/uct/ib/configure.m4 \
	$(top_srcdir)/src/uct/ib/rdmacm/configure.m4 \
	$(top_srcdir)/src/uct/rocm/configure.m4 \
	$(top_srcdir)/src/uct/rocm/gdr/configure.m4 \
	$(top_srcdir)/src/uct/sm/configure.m4 \
	$(top_srcdir)/src/uct/sm/scopy/configure.m4 \
	$(top_srcdir)/src/uct/sm/scopy/cma/configure.m4 \
	$(top_srcdir)/src/uct/sm/scopy/knem/configure.m4 \
	$(top_srcdir)/src/uct/sm/mm/configure.m4 \
	$(top_srcdir)/src/uct/sm/mm/xpmem/configure.m4 \
	$(top_srcdir)/src/uct/ugni/configure.m4 \
	$(top_srcdir)/src/tools/perf/configure.m4 \
	$(top_srcdir)/src/tools/perf/lib/configure.m4 \
	$(top_srcdir)/src/tools/perf/cuda/configure.m4 \
	$(top_srcdir)/src/tools/perf/rocm/configure.m4 \
	$(top_srcdir)/test/gtest/configure.m4 \
	$(top_srcdir)/test/gtest/common/googletest/configure.m4 \
	$(top_srcdir)/test/gtest/ucm/test_dlopen/configure.m4 \
	$(top_srcdir)/test/gtest/ucm/test_dlopen/rpath-subdir/configure.m4 \
	$(top_srcdir)/test/gtest/ucs/test_module/configure.m4 \
	$(top_srcdir)/test/apps/iodemo/configure.m4 \
	$(top_srcdir)/test/apps/uct_info/configure.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(nobase_dist_libucs_la_HEADERS) \
	$(noinst_HEADERS) $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES = ucx-ucs.pc
CONFIG_CLEAN_VPATH_FILES =
@HAVE_STATS_TRUE@am__EXEEXT_1 = ucs_stats_parser$(EXEEXT)
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" \
	"$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(libucs_ladir)"
PROGRAMS = $(bin_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
LTLIBRARIES = $(lib_LTLIBRARIES)
am__DEPENDENCIES_1 =
libucs_la_DEPENDENCIES = $(am__DEPENDENCIES_1) \
	$(top_builddir)/src/ucm/libucm.la $(am__DEPENDENCIES_1)
am__libucs_la_SOURCES_DIST = algorithm/crc.c algorithm/qsort_r.c \
	arch/aarch64/cpu.c arch/aarch64/global_opts.c \
	arch/ppc64/timebase.c arch/ppc64/global_opts.c \
	arch/x86_64/cpu.c arch/x86_64/global_opts.c arch/cpu.c \
	async/async.c async/signal.c async/pipe.c async/thread.c \
	config/global_opts.c config/ucm_opts.c config/ini.c \
	config/parser.c datastruct/arbiter.c datastruct/array.c \
	datastruct/callbackq.c datastruct/frag_list.c \
	datastruct/mpmc.c datastruct/mpool.c datastruct/mpool_set.c \
	datastruct/pgtable.c datastruct/ptr_array.c \
	datastruct/ptr_map.c datastruct/strided_alloc.c \
	datastruct/string_buffer.c datastruct/string_set.c \
	datastruct/conn_match.c debug/assert.c debug/debug.c \
	debug/log.c debug/memtrack.c memory/memory_type.c \
	memory/memtype_cache.c memory/numa.c memory/rcache.c \
	memory/rcache_vfs.c profile/profile.c stats/stats.c \
	sys/event_set.c sys/init.c sys/math.c sys/module.c \
	sys/string.c sys/sys.c sys/iovec.c sys/lib.c sys/sock.c \
	sys/topo/base/topo.c sys/stubs.c sys/uid.c time/time.c \
	time/timer_wheel.c time/timerq.c type/class.c type/status.c \
	type/spinlock.c type/thread_mode.c vfs/base/vfs_obj.c \
	vfs/base/vfs_cb.c arch/aarch64/memcpy_thunderx2.S \
	stats/client_server.c stats/serialization.c stats/libstats.c
am__dirstamp = $(am__leading_dot)dirstamp
@HAVE_AARCH64_THUNDERX2_TRUE@am__objects_1 = arch/aarch64/libucs_la-memcpy_thunderx2.lo
@HAVE_STATS_TRUE@am__objects_2 = stats/libucs_la-client_server.lo \
@HAVE_STATS_TRUE@	stats/libucs_la-serialization.lo \
@HAVE_STATS_TRUE@	stats/libucs_la-libstats.lo
am_libucs_la_OBJECTS = algorithm/libucs_la-crc.lo \
	algorithm/libucs_la-qsort_r.lo arch/aarch64/libucs_la-cpu.lo \
	arch/aarch64/libucs_la-global_opts.lo \
	arch/ppc64/libucs_la-timebase.lo \
	arch/ppc64/libucs_la-global_opts.lo \
	arch/x86_64/libucs_la-cpu.lo \
	arch/x86_64/libucs_la-global_opts.lo arch/libucs_la-cpu.lo \
	async/libucs_la-async.lo async/libucs_la-signal.lo \
	async/libucs_la-pipe.lo async/libucs_la-thread.lo \
	config/libucs_la-global_opts.lo config/libucs_la-ucm_opts.lo \
	config/libucs_la-ini.lo config/libucs_la-parser.lo \
	datastruct/libucs_la-arbiter.lo datastruct/libucs_la-array.lo \
	datastruct/libucs_la-callbackq.lo \
	datastruct/libucs_la-frag_list.lo datastruct/libucs_la-mpmc.lo \
	datastruct/libucs_la-mpool.lo \
	datastruct/libucs_la-mpool_set.lo \
	datastruct/libucs_la-pgtable.lo \
	datastruct/libucs_la-ptr_array.lo \
	datastruct/libucs_la-ptr_map.lo \
	datastruct/libucs_la-strided_alloc.lo \
	datastruct/libucs_la-string_buffer.lo \
	datastruct/libucs_la-string_set.lo \
	datastruct/libucs_la-conn_match.lo debug/libucs_la-assert.lo \
	debug/libucs_la-debug.lo debug/libucs_la-log.lo \
	debug/libucs_la-memtrack.lo memory/libucs_la-memory_type.lo \
	memory/libucs_la-memtype_cache.lo memory/libucs_la-numa.lo \
	memory/libucs_la-rcache.lo memory/libucs_la-rcache_vfs.lo \
	profile/libucs_la-profile.lo stats/libucs_la-stats.lo \
	sys/libucs_la-event_set.lo sys/libucs_la-init.lo \
	sys/libucs_la-math.lo sys/libucs_la-module.lo \
	sys/libucs_la-string.lo sys/libucs_la-sys.lo \
	sys/libucs_la-iovec.lo sys/libucs_la-lib.lo \
	sys/libucs_la-sock.lo sys/topo/base/libucs_la-topo.lo \
	sys/libucs_la-stubs.lo sys/libucs_la-uid.lo \
	time/libucs_la-time.lo time/libucs_la-timer_wheel.lo \
	time/libucs_la-timerq.lo type/libucs_la-class.lo \
	type/libucs_la-status.lo type/libucs_la-spinlock.lo \
	type/libucs_la-thread_mode.lo vfs/base/libucs_la-vfs_obj.lo \
	vfs/base/libucs_la-vfs_cb.lo $(am__objects_1) $(am__objects_2)
libucs_la_OBJECTS = $(am_libucs_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libucs_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(libucs_la_CFLAGS) \
	$(CFLAGS) $(libucs_la_LDFLAGS) $(LDFLAGS) -o $@
am__ucs_stats_parser_SOURCES_DIST = stats/stats_parser.c
@HAVE_STATS_TRUE@am_ucs_stats_parser_OBJECTS = stats/ucs_stats_parser-stats_parser.$(OBJEXT)
ucs_stats_parser_OBJECTS = $(am_ucs_stats_parser_OBJECTS)
@HAVE_STATS_TRUE@ucs_stats_parser_DEPENDENCIES = libucs.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = 
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = algorithm/$(DEPDIR)/libucs_la-crc.Plo \
	algorithm/$(DEPDIR)/libucs_la-qsort_r.Plo \
	arch/$(DEPDIR)/libucs_la-cpu.Plo \
	arch/aarch64/$(DEPDIR)/libucs_la-cpu.Plo \
	arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Plo \
	arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Plo \
	arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Plo \
	arch/ppc64/$(DEPDIR)/libucs_la-timebase.Plo \
	arch/x86_64/$(DEPDIR)/libucs_la-cpu.Plo \
	arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Plo \
	async/$(DEPDIR)/libucs_la-async.Plo \
	async/$(DEPDIR)/libucs_la-pipe.Plo \
	async/$(DEPDIR)/libucs_la-signal.Plo \
	async/$(DEPDIR)/libucs_la-thread.Plo \
	config/$(DEPDIR)/libucs_la-global_opts.Plo \
	config/$(DEPDIR)/libucs_la-ini.Plo \
	config/$(DEPDIR)/libucs_la-parser.Plo \
	config/$(DEPDIR)/libucs_la-ucm_opts.Plo \
	datastruct/$(DEPDIR)/libucs_la-arbiter.Plo \
	datastruct/$(DEPDIR)/libucs_la-array.Plo \
	datastruct/$(DEPDIR)/libucs_la-callbackq.Plo \
	datastruct/$(DEPDIR)/libucs_la-conn_match.Plo \
	datastruct/$(DEPDIR)/libucs_la-frag_list.Plo \
	datastruct/$(DEPDIR)/libucs_la-mpmc.Plo \
	datastruct/$(DEPDIR)/libucs_la-mpool.Plo \
	datastruct/$(DEPDIR)/libucs_la-mpool_set.Plo \
	datastruct/$(DEPDIR)/libucs_la-pgtable.Plo \
	datastruct/$(DEPDIR)/libucs_la-ptr_array.Plo \
	datastruct/$(DEPDIR)/libucs_la-ptr_map.Plo \
	datastruct/$(DEPDIR)/libucs_la-strided_alloc.Plo \
	datastruct/$(DEPDIR)/libucs_la-string_buffer.Plo \
	datastruct/$(DEPDIR)/libucs_la-string_set.Plo \
	debug/$(DEPDIR)/libucs_la-assert.Plo \
	debug/$(DEPDIR)/libucs_la-debug.Plo \
	debug/$(DEPDIR)/libucs_la-log.Plo \
	debug/$(DEPDIR)/libucs_la-memtrack.Plo \
	memory/$(DEPDIR)/libucs_la-memory_type.Plo \
	memory/$(DEPDIR)/libucs_la-memtype_cache.Plo \
	memory/$(DEPDIR)/libucs_la-numa.Plo \
	memory/$(DEPDIR)/libucs_la-rcache.Plo \
	memory/$(DEPDIR)/libucs_la-rcache_vfs.Plo \
	profile/$(DEPDIR)/libucs_la-profile.Plo \
	stats/$(DEPDIR)/libucs_la-client_server.Plo \
	stats/$(DEPDIR)/libucs_la-libstats.Plo \
	stats/$(DEPDIR)/libucs_la-serialization.Plo \
	stats/$(DEPDIR)/libucs_la-stats.Plo \
	stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po \
	sys/$(DEPDIR)/libucs_la-event_set.Plo \
	sys/$(DEPDIR)/libucs_la-init.Plo \
	sys/$(DEPDIR)/libucs_la-iovec.Plo \
	sys/$(DEPDIR)/libucs_la-lib.Plo \
	sys/$(DEPDIR)/libucs_la-math.Plo \
	sys/$(DEPDIR)/libucs_la-module.Plo \
	sys/$(DEPDIR)/libucs_la-sock.Plo \
	sys/$(DEPDIR)/libucs_la-string.Plo \
	sys/$(DEPDIR)/libucs_la-stubs.Plo \
	sys/$(DEPDIR)/libucs_la-sys.Plo \
	sys/$(DEPDIR)/libucs_la-uid.Plo \
	sys/topo/base/$(DEPDIR)/libucs_la-topo.Plo \
	time/$(DEPDIR)/libucs_la-time.Plo \
	time/$(DEPDIR)/libucs_la-timer_wheel.Plo \
	time/$(DEPDIR)/libucs_la-timerq.Plo \
	type/$(DEPDIR)/libucs_la-class.Plo \
	type/$(DEPDIR)/libucs_la-spinlock.Plo \
	type/$(DEPDIR)/libucs_la-status.Plo \
	type/$(DEPDIR)/libucs_la-thread_mode.Plo \
	vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Plo \
	vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Plo
am__mv = mv -f
CPPASCOMPILE = $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS)
LTCPPASCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CCASFLAGS) $(CCASFLAGS)
AM_V_CPPAS = $(am__v_CPPAS_@AM_V@)
am__v_CPPAS_ = $(am__v_CPPAS_@AM_DEFAULT_V@)
am__v_CPPAS_0 = @echo "  CPPAS   " $@;
am__v_CPPAS_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libucs_la_SOURCES) $(ucs_stats_parser_SOURCES)
DIST_SOURCES = $(am__libucs_la_SOURCES_DIST) \
	$(am__ucs_stats_parser_SOURCES_DIST)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(pkgconfig_DATA)
HEADERS = $(nobase_dist_libucs_la_HEADERS) $(noinst_HEADERS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
DIST_SUBDIRS = $(SUBDIRS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/ucx-ucs.pc.in \
	$(top_srcdir)/config/module-pkg-config.am \
	$(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = @ACLOCAL@
ALLOCA = @ALLOCA@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BASE_CFLAGS = @BASE_CFLAGS@
BASE_CPPFLAGS = @BASE_CPPFLAGS@
BASE_CXXFLAGS = @BASE_CXXFLAGS@
BFD_CFLAGS = @BFD_CFLAGS@
BFD_CPPFLAGS = @BFD_CPPFLAGS@
BFD_DEPS = @BFD_DEPS@
BFD_LDFLAGS = @BFD_LDFLAGS@
BFD_LIBS = @BFD_LIBS@
CC = @CC@
CCAS = @CCAS@
CCASDEPMODE = @CCASDEPMODE@
CCASFLAGS = @CCASFLAGS@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CFLAGS_NO_DEPRECATED = @CFLAGS_NO_DEPRECATED@
CFLAGS_PEDANTIC = @CFLAGS_PEDANTIC@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CRAY_UGNI_CFLAGS = @CRAY_UGNI_CFLAGS@
CRAY_UGNI_LIBS = @CRAY_UGNI_LIBS@
CUDA_CPPFLAGS = @CUDA_CPPFLAGS@
CUDA_LDFLAGS = @CUDA_LDFLAGS@
CUDA_LIBS = @CUDA_LIBS@
CUDA_STATIC_LIBS = @CUDA_STATIC_LIBS@
CXX = @CXX@
CXX11FLAGS = @CXX11FLAGS@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DOXYGEN_PAPER_SIZE = @DOXYGEN_PAPER_SIZE@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
DX_BIBTEX = @DX_BIBTEX@
DX_CONFIG = @DX_CONFIG@
DX_DOCDIR = @DX_DOCDIR@
DX_DOT = @DX_DOT@
DX_DOXYGEN = @DX_DOXYGEN@
DX_DVIPS = @DX_DVIPS@
DX_EGREP = @DX_EGREP@
DX_ENV = @DX_ENV@
DX_FLAG_chi = @DX_FLAG_chi@
DX_FLAG_chm = @DX_FLAG_chm@
DX_FLAG_doc = @DX_FLAG_doc@
DX_FLAG_dot = @DX_FLAG_dot@
DX_FLAG_html = @DX_FLAG_html@
DX_FLAG_man = @DX_FLAG_man@
DX_FLAG_pdf = @DX_FLAG_pdf@
DX_FLAG_ps = @DX_FLAG_ps@
DX_FLAG_rtf = @DX_FLAG_rtf@
DX_FLAG_xml = @DX_FLAG_xml@
DX_HHC = @DX_HHC@
DX_LATEX = @DX_LATEX@
DX_MAKEINDEX = @DX_MAKEINDEX@
DX_PDFLATEX = @DX_PDFLATEX@
DX_PERL = @DX_PERL@
DX_PROJECT = @DX_PROJECT@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
EXTRA_VERSION = @EXTRA_VERSION@
FGREP = @FGREP@
FUSE3_CPPFLAGS = @FUSE3_CPPFLAGS@
FUSE3_LDFLAGS = @FUSE3_LDFLAGS@
FUSE3_LIBS = @FUSE3_LIBS@
GDR_COPY_CPPFLAGS = @GDR_COPY_CPPFLAGS@
GDR_COPY_LDFLAGS = @GDR_COPY_LDFLAGS@
GITBIN = @GITBIN@
GO = @GO@
GOBIN = @GOBIN@
GRAPHVIZ_DOT = @GRAPHVIZ_DOT@
GREP = @GREP@
GTEST_CXXFLAGS = @GTEST_CXXFLAGS@
HIP_CPPFLAGS = @HIP_CPPFLAGS@
HIP_CXXFLAGS = @HIP_CXXFLAGS@
HIP_LDFLAGS = @HIP_LDFLAGS@
HIP_LIBS = @HIP_LIBS@
IBVERBS_CFLAGS = @IBVERBS_CFLAGS@
IBVERBS_CPPFLAGS = @IBVERBS_CPPFLAGS@
IBVERBS_DIR = @IBVERBS_DIR@
IBVERBS_LDFLAGS = @IBVERBS_LDFLAGS@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
JAVABIN = @JAVABIN@
JDK = @JDK@
KNEM_CPPFLAGS = @KNEM_CPPFLAGS@
LD = @LD@
LDFLAGS = @LDFLAGS@
LDFLAGS_DYNAMIC_LIST_DATA = @LDFLAGS_DYNAMIC_LIST_DATA@
LIBM = @LIBM@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIB_MLX5 = @LIB_MLX5@
LIPO = @LIPO@
LN_RS = @LN_RS@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAJOR_VERSION = @MAJOR_VERSION@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MINOR_VERSION = @MINOR_VERSION@
MKDIR_P = @MKDIR_P@
MPICC = @MPICC@
MPIRUN = @MPIRUN@
MVN = @MVN@
MVNBIN = @MVNBIN@
NM = @NM@
NMEDIT = @NMEDIT@
NUMA_LIBS = @NUMA_LIBS@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPENMP_CFLAGS = @OPENMP_CFLAGS@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATCH_VERSION = @PATCH_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PERF_LIB_CXXFLAGS = @PERF_LIB_CXXFLAGS@
PKG_CONFIG = @PKG_CONFIG@
RANLIB = @RANLIB@
RDMACM_CPPFLAGS = @RDMACM_CPPFLAGS@
RDMACM_LDFLAGS = @RDMACM_LDFLAGS@
RDMACM_LIBS = @RDMACM_LIBS@
READLINK = @READLINK@
ROCM_CPPFLAGS = @ROCM_CPPFLAGS@
ROCM_LDFLAGS = @ROCM_LDFLAGS@
ROCM_LIBS = @ROCM_LIBS@
ROCM_ROOT = @ROCM_ROOT@
RTE_CPPFLAGS = @RTE_CPPFLAGS@
RTE_LDFLAGS = @RTE_LDFLAGS@
SCM_BRANCH = @SCM_BRANCH@
SCM_VERSION = @SCM_VERSION@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SOVERSION = @SOVERSION@
STRIP = @STRIP@
UCG_SUBDIR = @UCG_SUBDIR@
UCM_MODULE_LDFLAGS = @UCM_MODULE_LDFLAGS@
UCX_PERFTEST_CC = @UCX_PERFTEST_CC@
VALGRIND_LIBPATH = @VALGRIND_LIBPATH@
VERSION = @VERSION@
XPMEM_CFLAGS = @XPMEM_CFLAGS@
XPMEM_LIBS = @XPMEM_LIBS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_bindings = @build_bindings@
build_cpu = @build_cpu@
build_modules = @build_modules@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localmoduledir = @localmoduledir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
moduledir = @moduledir@
modulesubdir = @modulesubdir@
objdir = @objdir@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
shrext = @shrext@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
top_top_srcdir = @top_top_srcdir@
ucx_config_dir = @ucx_config_dir@
SUBDIRS = vfs/sock . vfs/fuse signal
AUTOMAKE_OPTIONS = nostdinc # avoid collision with built-in debug.h
lib_LTLIBRARIES = libucs.la
libucs_la_CPPFLAGS = $(BASE_CPPFLAGS) $(BFD_CPPFLAGS) \
                     -DUCX_MODULE_DIR=\"$(moduledir)\" \
                     -DUCX_CONFIG_DIR=\"$(ucx_config_dir)\"

libucs_la_CFLAGS = $(BASE_CFLAGS) $(BFD_CFLAGS)
libucs_la_LDFLAGS = -ldl $(NUMA_LIBS) $(BFD_LDFLAGS) -version-info $(SOVERSION)
libucs_ladir = $(includedir)/ucs
libucs_la_LIBADD = $(LIBM) $(top_builddir)/src/ucm/libucm.la $(BFD_LIBS)
nobase_dist_libucs_la_HEADERS = \
	arch/aarch64/bitops.h \
	arch/ppc64/bitops.h \
	arch/x86_64/bitops.h \
	arch/bitops.h \
	algorithm/crc.h \
	algorithm/qsort_r.h \
	async/async_fwd.h \
	config/global_opts.h \
	config/ini.h \
	config/parser.h \
	config/types.h \
	datastruct/array.h \
	datastruct/array.inl \
	datastruct/callbackq.h \
	datastruct/hlist.h \
	datastruct/khash.h \
	datastruct/linear_func.h \
	datastruct/list.h \
	datastruct/mpool.h \
	datastruct/mpool_set.h \
	datastruct/pgtable.h \
	datastruct/queue_types.h \
	datastruct/strided_alloc.h \
	datastruct/string_buffer.h \
	datastruct/string_set.h \
	debug/log_def.h \
	debug/debug.h \
	debug/memtrack.h \
	memory/rcache.h \
	memory/memory_type.h \
	memory/memtype_cache.h \
	profile/profile_defs.h \
	profile/profile_off.h \
	profile/profile_on.h \
	stats/stats_fwd.h \
	stats/libstats.h \
	sys/event_set.h \
	sys/compiler_def.h\
	sys/math.h \
	sys/preprocessor.h \
	sys/string.h \
	sys/sock.h \
	sys/topo/base/topo.h \
	sys/stubs.h \
	sys/uid.h \
	time/time_def.h \
	type/class.h \
	type/param.h \
	type/init_once.h \
	type/spinlock.h \
	type/status.h \
	type/thread_mode.h \
	type/cpu_set.h \
	vfs/base/vfs_obj.h \
	vfs/base/vfs_cb.h \
	arch/atomic.h \
	arch/x86_64/global_opts.h \
	arch/x86_64/atomic.h \
	arch/aarch64/global_opts.h \
	arch/generic/atomic.h \
	arch/ppc64/global_opts.h \
	arch/global_opts.h

noinst_HEADERS = \
	arch/aarch64/cpu.h \
	arch/generic/cpu.h \
	arch/ppc64/cpu.h \
	arch/x86_64/cpu.h \
	arch/cpu.h \
	config/ucm_opts.h \
	datastruct/arbiter.h \
	datastruct/bitmap.h \
	datastruct/frag_list.h \
	datastruct/mpmc.h \
	datastruct/mpool.inl \
	datastruct/mpool_set.inl \
	datastruct/ptr_array.h \
	datastruct/queue.h \
	datastruct/sglib.h \
	datastruct/sglib_wrapper.h \
	datastruct/conn_match.h \
	datastruct/ptr_map.h \
	datastruct/ptr_map.inl \
	debug/assert.h \
	debug/debug_int.h \
	debug/log.h \
	debug/memtrack_int.h \
	memory/numa.h \
	memory/rcache_int.h \
	profile/profile.h \
	stats/stats.h \
	sys/checker.h \
	sys/compiler.h \
	sys/lib.h \
	sys/module.h \
	sys/sys.h \
	sys/iovec.h \
	sys/iovec.inl \
	time/time.h \
	time/timerq.h \
	time/timer_wheel.h \
	type/serialize.h \
	type/float8.h \
	async/async.h \
	async/pipe.h \
	async/signal.h \
	async/thread.h \
	async/async_int.h

libucs_la_SOURCES = algorithm/crc.c algorithm/qsort_r.c \
	arch/aarch64/cpu.c arch/aarch64/global_opts.c \
	arch/ppc64/timebase.c arch/ppc64/global_opts.c \
	arch/x86_64/cpu.c arch/x86_64/global_opts.c arch/cpu.c \
	async/async.c async/signal.c async/pipe.c async/thread.c \
	config/global_opts.c config/ucm_opts.c config/ini.c \
	config/parser.c datastruct/arbiter.c datastruct/array.c \
	datastruct/callbackq.c datastruct/frag_list.c \
	datastruct/mpmc.c datastruct/mpool.c datastruct/mpool_set.c \
	datastruct/pgtable.c datastruct/ptr_array.c \
	datastruct/ptr_map.c datastruct/strided_alloc.c \
	datastruct/string_buffer.c datastruct/string_set.c \
	datastruct/conn_match.c debug/assert.c debug/debug.c \
	debug/log.c debug/memtrack.c memory/memory_type.c \
	memory/memtype_cache.c memory/numa.c memory/rcache.c \
	memory/rcache_vfs.c profile/profile.c stats/stats.c \
	sys/event_set.c sys/init.c sys/math.c sys/module.c \
	sys/string.c sys/sys.c sys/iovec.c sys/lib.c sys/sock.c \
	sys/topo/base/topo.c sys/stubs.c sys/uid.c time/time.c \
	time/timer_wheel.c time/timerq.c type/class.c type/status.c \
	type/spinlock.c type/thread_mode.c vfs/base/vfs_obj.c \
	vfs/base/vfs_cb.c $(am__append_1) $(am__append_2)
@HAVE_STATS_TRUE@ucs_stats_parser_CPPFLAGS = $(BASE_CPPFLAGS)
@HAVE_STATS_TRUE@ucs_stats_parser_LDADD = libucs.la
@HAVE_STATS_TRUE@ucs_stats_parser_SOURCES = stats/stats_parser.c
PKG_CONFIG_NAME = ucs
EXTRA_DIST = ucx-$(PKG_CONFIG_NAME).pc.in
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = ucx-$(PKG_CONFIG_NAME).pc
all: all-recursive

.SUFFIXES:
.SUFFIXES: .S .c .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am $(top_srcdir)/config/module-pkg-config.am $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/ucs/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/ucs/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(top_srcdir)/config/module-pkg-config.am $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
ucx-ucs.pc: $(top_builddir)/config.status $(srcdir)/ucx-ucs.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
algorithm/$(am__dirstamp):
	@$(MKDIR_P) algorithm
	@: > algorithm/$(am__dirstamp)
algorithm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) algorithm/$(DEPDIR)
	@: > algorithm/$(DEPDIR)/$(am__dirstamp)
algorithm/libucs_la-crc.lo: algorithm/$(am__dirstamp) \
	algorithm/$(DEPDIR)/$(am__dirstamp)
algorithm/libucs_la-qsort_r.lo: algorithm/$(am__dirstamp) \
	algorithm/$(DEPDIR)/$(am__dirstamp)
arch/aarch64/$(am__dirstamp):
	@$(MKDIR_P) arch/aarch64
	@: > arch/aarch64/$(am__dirstamp)
arch/aarch64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) arch/aarch64/$(DEPDIR)
	@: > arch/aarch64/$(DEPDIR)/$(am__dirstamp)
arch/aarch64/libucs_la-cpu.lo: arch/aarch64/$(am__dirstamp) \
	arch/aarch64/$(DEPDIR)/$(am__dirstamp)
arch/aarch64/libucs_la-global_opts.lo: arch/aarch64/$(am__dirstamp) \
	arch/aarch64/$(DEPDIR)/$(am__dirstamp)
arch/ppc64/$(am__dirstamp):
	@$(MKDIR_P) arch/ppc64
	@: > arch/ppc64/$(am__dirstamp)
arch/ppc64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) arch/ppc64/$(DEPDIR)
	@: > arch/ppc64/$(DEPDIR)/$(am__dirstamp)
arch/ppc64/libucs_la-timebase.lo: arch/ppc64/$(am__dirstamp) \
	arch/ppc64/$(DEPDIR)/$(am__dirstamp)
arch/ppc64/libucs_la-global_opts.lo: arch/ppc64/$(am__dirstamp) \
	arch/ppc64/$(DEPDIR)/$(am__dirstamp)
arch/x86_64/$(am__dirstamp):
	@$(MKDIR_P) arch/x86_64
	@: > arch/x86_64/$(am__dirstamp)
arch/x86_64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) arch/x86_64/$(DEPDIR)
	@: > arch/x86_64/$(DEPDIR)/$(am__dirstamp)
arch/x86_64/libucs_la-cpu.lo: arch/x86_64/$(am__dirstamp) \
	arch/x86_64/$(DEPDIR)/$(am__dirstamp)
arch/x86_64/libucs_la-global_opts.lo: arch/x86_64/$(am__dirstamp) \
	arch/x86_64/$(DEPDIR)/$(am__dirstamp)
arch/$(am__dirstamp):
	@$(MKDIR_P) arch
	@: > arch/$(am__dirstamp)
arch/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) arch/$(DEPDIR)
	@: > arch/$(DEPDIR)/$(am__dirstamp)
arch/libucs_la-cpu.lo: arch/$(am__dirstamp) \
	arch/$(DEPDIR)/$(am__dirstamp)
async/$(am__dirstamp):
	@$(MKDIR_P) async
	@: > async/$(am__dirstamp)
async/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) async/$(DEPDIR)
	@: > async/$(DEPDIR)/$(am__dirstamp)
async/libucs_la-async.lo: async/$(am__dirstamp) \
	async/$(DEPDIR)/$(am__dirstamp)
async/libucs_la-signal.lo: async/$(am__dirstamp) \
	async/$(DEPDIR)/$(am__dirstamp)
async/libucs_la-pipe.lo: async/$(am__dirstamp) \
	async/$(DEPDIR)/$(am__dirstamp)
async/libucs_la-thread.lo: async/$(am__dirstamp) \
	async/$(DEPDIR)/$(am__dirstamp)
config/$(am__dirstamp):
	@$(MKDIR_P) config
	@: > config/$(am__dirstamp)
config/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) config/$(DEPDIR)
	@: > config/$(DEPDIR)/$(am__dirstamp)
config/libucs_la-global_opts.lo: config/$(am__dirstamp) \
	config/$(DEPDIR)/$(am__dirstamp)
config/libucs_la-ucm_opts.lo: config/$(am__dirstamp) \
	config/$(DEPDIR)/$(am__dirstamp)
config/libucs_la-ini.lo: config/$(am__dirstamp) \
	config/$(DEPDIR)/$(am__dirstamp)
config/libucs_la-parser.lo: config/$(am__dirstamp) \
	config/$(DEPDIR)/$(am__dirstamp)
datastruct/$(am__dirstamp):
	@$(MKDIR_P) datastruct
	@: > datastruct/$(am__dirstamp)
datastruct/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) datastruct/$(DEPDIR)
	@: > datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-arbiter.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-array.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-callbackq.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-frag_list.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-mpmc.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-mpool.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-mpool_set.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-pgtable.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-ptr_array.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-ptr_map.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-strided_alloc.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-string_buffer.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-string_set.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
datastruct/libucs_la-conn_match.lo: datastruct/$(am__dirstamp) \
	datastruct/$(DEPDIR)/$(am__dirstamp)
debug/$(am__dirstamp):
	@$(MKDIR_P) debug
	@: > debug/$(am__dirstamp)
debug/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) debug/$(DEPDIR)
	@: > debug/$(DEPDIR)/$(am__dirstamp)
debug/libucs_la-assert.lo: debug/$(am__dirstamp) \
	debug/$(DEPDIR)/$(am__dirstamp)
debug/libucs_la-debug.lo: debug/$(am__dirstamp) \
	debug/$(DEPDIR)/$(am__dirstamp)
debug/libucs_la-log.lo: debug/$(am__dirstamp) \
	debug/$(DEPDIR)/$(am__dirstamp)
debug/libucs_la-memtrack.lo: debug/$(am__dirstamp) \
	debug/$(DEPDIR)/$(am__dirstamp)
memory/$(am__dirstamp):
	@$(MKDIR_P) memory
	@: > memory/$(am__dirstamp)
memory/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) memory/$(DEPDIR)
	@: > memory/$(DEPDIR)/$(am__dirstamp)
memory/libucs_la-memory_type.lo: memory/$(am__dirstamp) \
	memory/$(DEPDIR)/$(am__dirstamp)
memory/libucs_la-memtype_cache.lo: memory/$(am__dirstamp) \
	memory/$(DEPDIR)/$(am__dirstamp)
memory/libucs_la-numa.lo: memory/$(am__dirstamp) \
	memory/$(DEPDIR)/$(am__dirstamp)
memory/libucs_la-rcache.lo: memory/$(am__dirstamp) \
	memory/$(DEPDIR)/$(am__dirstamp)
memory/libucs_la-rcache_vfs.lo: memory/$(am__dirstamp) \
	memory/$(DEPDIR)/$(am__dirstamp)
profile/$(am__dirstamp):
	@$(MKDIR_P) profile
	@: > profile/$(am__dirstamp)
profile/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) profile/$(DEPDIR)
	@: > profile/$(DEPDIR)/$(am__dirstamp)
profile/libucs_la-profile.lo: profile/$(am__dirstamp) \
	profile/$(DEPDIR)/$(am__dirstamp)
stats/$(am__dirstamp):
	@$(MKDIR_P) stats
	@: > stats/$(am__dirstamp)
stats/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) stats/$(DEPDIR)
	@: > stats/$(DEPDIR)/$(am__dirstamp)
stats/libucs_la-stats.lo: stats/$(am__dirstamp) \
	stats/$(DEPDIR)/$(am__dirstamp)
sys/$(am__dirstamp):
	@$(MKDIR_P) sys
	@: > sys/$(am__dirstamp)
sys/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) sys/$(DEPDIR)
	@: > sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-event_set.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-init.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-math.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-module.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-string.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-sys.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-iovec.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-lib.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-sock.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/topo/base/$(am__dirstamp):
	@$(MKDIR_P) sys/topo/base
	@: > sys/topo/base/$(am__dirstamp)
sys/topo/base/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) sys/topo/base/$(DEPDIR)
	@: > sys/topo/base/$(DEPDIR)/$(am__dirstamp)
sys/topo/base/libucs_la-topo.lo: sys/topo/base/$(am__dirstamp) \
	sys/topo/base/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-stubs.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
sys/libucs_la-uid.lo: sys/$(am__dirstamp) \
	sys/$(DEPDIR)/$(am__dirstamp)
time/$(am__dirstamp):
	@$(MKDIR_P) time
	@: > time/$(am__dirstamp)
time/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) time/$(DEPDIR)
	@: > time/$(DEPDIR)/$(am__dirstamp)
time/libucs_la-time.lo: time/$(am__dirstamp) \
	time/$(DEPDIR)/$(am__dirstamp)
time/libucs_la-timer_wheel.lo: time/$(am__dirstamp) \
	time/$(DEPDIR)/$(am__dirstamp)
time/libucs_la-timerq.lo: time/$(am__dirstamp) \
	time/$(DEPDIR)/$(am__dirstamp)
type/$(am__dirstamp):
	@$(MKDIR_P) type
	@: > type/$(am__dirstamp)
type/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) type/$(DEPDIR)
	@: > type/$(DEPDIR)/$(am__dirstamp)
type/libucs_la-class.lo: type/$(am__dirstamp) \
	type/$(DEPDIR)/$(am__dirstamp)
type/libucs_la-status.lo: type/$(am__dirstamp) \
	type/$(DEPDIR)/$(am__dirstamp)
type/libucs_la-spinlock.lo: type/$(am__dirstamp) \
	type/$(DEPDIR)/$(am__dirstamp)
type/libucs_la-thread_mode.lo: type/$(am__dirstamp) \
	type/$(DEPDIR)/$(am__dirstamp)
vfs/base/$(am__dirstamp):
	@$(MKDIR_P) vfs/base
	@: > vfs/base/$(am__dirstamp)
vfs/base/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) vfs/base/$(DEPDIR)
	@: > vfs/base/$(DEPDIR)/$(am__dirstamp)
vfs/base/libucs_la-vfs_obj.lo: vfs/base/$(am__dirstamp) \
	vfs/base/$(DEPDIR)/$(am__dirstamp)
vfs/base/libucs_la-vfs_cb.lo: vfs/base/$(am__dirstamp) \
	vfs/base/$(DEPDIR)/$(am__dirstamp)
arch/aarch64/libucs_la-memcpy_thunderx2.lo:  \
	arch/aarch64/$(am__dirstamp) \
	arch/aarch64/$(DEPDIR)/$(am__dirstamp)
stats/libucs_la-client_server.lo: stats/$(am__dirstamp) \
	stats/$(DEPDIR)/$(am__dirstamp)
stats/libucs_la-serialization.lo: stats/$(am__dirstamp) \
	stats/$(DEPDIR)/$(am__dirstamp)
stats/libucs_la-libstats.lo: stats/$(am__dirstamp) \
	stats/$(DEPDIR)/$(am__dirstamp)

libucs.la: $(libucs_la_OBJECTS) $(libucs_la_DEPENDENCIES) $(EXTRA_libucs_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libucs_la_LINK) -rpath $(libdir) $(libucs_la_OBJECTS) $(libucs_la_LIBADD) $(LIBS)
stats/ucs_stats_parser-stats_parser.$(OBJEXT): stats/$(am__dirstamp) \
	stats/$(DEPDIR)/$(am__dirstamp)

ucs_stats_parser$(EXEEXT): $(ucs_stats_parser_OBJECTS) $(ucs_stats_parser_DEPENDENCIES) $(EXTRA_ucs_stats_parser_DEPENDENCIES) 
	@rm -f ucs_stats_parser$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(ucs_stats_parser_OBJECTS) $(ucs_stats_parser_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f algorithm/*.$(OBJEXT)
	-rm -f algorithm/*.lo
	-rm -f arch/*.$(OBJEXT)
	-rm -f arch/*.lo
	-rm -f arch/aarch64/*.$(OBJEXT)
	-rm -f arch/aarch64/*.lo
	-rm -f arch/ppc64/*.$(OBJEXT)
	-rm -f arch/ppc64/*.lo
	-rm -f arch/x86_64/*.$(OBJEXT)
	-rm -f arch/x86_64/*.lo
	-rm -f async/*.$(OBJEXT)
	-rm -f async/*.lo
	-rm -f config/*.$(OBJEXT)
	-rm -f config/*.lo
	-rm -f datastruct/*.$(OBJEXT)
	-rm -f datastruct/*.lo
	-rm -f debug/*.$(OBJEXT)
	-rm -f debug/*.lo
	-rm -f memory/*.$(OBJEXT)
	-rm -f memory/*.lo
	-rm -f profile/*.$(OBJEXT)
	-rm -f profile/*.lo
	-rm -f stats/*.$(OBJEXT)
	-rm -f stats/*.lo
	-rm -f sys/*.$(OBJEXT)
	-rm -f sys/*.lo
	-rm -f sys/topo/base/*.$(OBJEXT)
	-rm -f sys/topo/base/*.lo
	-rm -f time/*.$(OBJEXT)
	-rm -f time/*.lo
	-rm -f type/*.$(OBJEXT)
	-rm -f type/*.lo
	-rm -f vfs/base/*.$(OBJEXT)
	-rm -f vfs/base/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@algorithm/$(DEPDIR)/libucs_la-crc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@algorithm/$(DEPDIR)/libucs_la-qsort_r.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/$(DEPDIR)/libucs_la-cpu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/aarch64/$(DEPDIR)/libucs_la-cpu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/ppc64/$(DEPDIR)/libucs_la-timebase.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/x86_64/$(DEPDIR)/libucs_la-cpu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@async/$(DEPDIR)/libucs_la-async.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@async/$(DEPDIR)/libucs_la-pipe.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@async/$(DEPDIR)/libucs_la-signal.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@async/$(DEPDIR)/libucs_la-thread.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@config/$(DEPDIR)/libucs_la-global_opts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@config/$(DEPDIR)/libucs_la-ini.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@config/$(DEPDIR)/libucs_la-parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@config/$(DEPDIR)/libucs_la-ucm_opts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-arbiter.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-array.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-callbackq.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-conn_match.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-frag_list.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-mpmc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-mpool.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-mpool_set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-pgtable.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-ptr_array.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-ptr_map.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-strided_alloc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-string_buffer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@datastruct/$(DEPDIR)/libucs_la-string_set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@debug/$(DEPDIR)/libucs_la-assert.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@debug/$(DEPDIR)/libucs_la-debug.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@debug/$(DEPDIR)/libucs_la-log.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@debug/$(DEPDIR)/libucs_la-memtrack.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@memory/$(DEPDIR)/libucs_la-memory_type.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@memory/$(DEPDIR)/libucs_la-memtype_cache.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@memory/$(DEPDIR)/libucs_la-numa.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@memory/$(DEPDIR)/libucs_la-rcache.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@memory/$(DEPDIR)/libucs_la-rcache_vfs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@profile/$(DEPDIR)/libucs_la-profile.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@stats/$(DEPDIR)/libucs_la-client_server.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@stats/$(DEPDIR)/libucs_la-libstats.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@stats/$(DEPDIR)/libucs_la-serialization.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@stats/$(DEPDIR)/libucs_la-stats.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-event_set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-iovec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-lib.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-math.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-module.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-sock.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-string.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-stubs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-sys.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/$(DEPDIR)/libucs_la-uid.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sys/topo/base/$(DEPDIR)/libucs_la-topo.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@time/$(DEPDIR)/libucs_la-time.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@time/$(DEPDIR)/libucs_la-timer_wheel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@time/$(DEPDIR)/libucs_la-timerq.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@type/$(DEPDIR)/libucs_la-class.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@type/$(DEPDIR)/libucs_la-spinlock.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@type/$(DEPDIR)/libucs_la-status.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@type/$(DEPDIR)/libucs_la-thread_mode.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.S.o:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ $<

.S.obj:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.S.lo:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCCAS_TRUE@	$(LTCPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(LTCPPASCOMPILE) -c -o $@ $<

arch/aarch64/libucs_la-memcpy_thunderx2.lo: arch/aarch64/memcpy_thunderx2.S
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS) -MT arch/aarch64/libucs_la-memcpy_thunderx2.lo -MD -MP -MF arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Tpo -c -o arch/aarch64/libucs_la-memcpy_thunderx2.lo `test -f 'arch/aarch64/memcpy_thunderx2.S' || echo '$(srcdir)/'`arch/aarch64/memcpy_thunderx2.S
@am__fastdepCCAS_TRUE@	$(AM_V_at)$(am__mv) arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Tpo arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Plo
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='arch/aarch64/memcpy_thunderx2.S' object='arch/aarch64/libucs_la-memcpy_thunderx2.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS) -c -o arch/aarch64/libucs_la-memcpy_thunderx2.lo `test -f 'arch/aarch64/memcpy_thunderx2.S' || echo '$(srcdir)/'`arch/aarch64/memcpy_thunderx2.S

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

algorithm/libucs_la-crc.lo: algorithm/crc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT algorithm/libucs_la-crc.lo -MD -MP -MF algorithm/$(DEPDIR)/libucs_la-crc.Tpo -c -o algorithm/libucs_la-crc.lo `test -f 'algorithm/crc.c' || echo '$(srcdir)/'`algorithm/crc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) algorithm/$(DEPDIR)/libucs_la-crc.Tpo algorithm/$(DEPDIR)/libucs_la-crc.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='algorithm/crc.c' object='algorithm/libucs_la-crc.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o algorithm/libucs_la-crc.lo `test -f 'algorithm/crc.c' || echo '$(srcdir)/'`algorithm/crc.c

algorithm/libucs_la-qsort_r.lo: algorithm/qsort_r.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT algorithm/libucs_la-qsort_r.lo -MD -MP -MF algorithm/$(DEPDIR)/libucs_la-qsort_r.Tpo -c -o algorithm/libucs_la-qsort_r.lo `test -f 'algorithm/qsort_r.c' || echo '$(srcdir)/'`algorithm/qsort_r.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) algorithm/$(DEPDIR)/libucs_la-qsort_r.Tpo algorithm/$(DEPDIR)/libucs_la-qsort_r.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='algorithm/qsort_r.c' object='algorithm/libucs_la-qsort_r.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o algorithm/libucs_la-qsort_r.lo `test -f 'algorithm/qsort_r.c' || echo '$(srcdir)/'`algorithm/qsort_r.c

arch/aarch64/libucs_la-cpu.lo: arch/aarch64/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/aarch64/libucs_la-cpu.lo -MD -MP -MF arch/aarch64/$(DEPDIR)/libucs_la-cpu.Tpo -c -o arch/aarch64/libucs_la-cpu.lo `test -f 'arch/aarch64/cpu.c' || echo '$(srcdir)/'`arch/aarch64/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/aarch64/$(DEPDIR)/libucs_la-cpu.Tpo arch/aarch64/$(DEPDIR)/libucs_la-cpu.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/aarch64/cpu.c' object='arch/aarch64/libucs_la-cpu.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/aarch64/libucs_la-cpu.lo `test -f 'arch/aarch64/cpu.c' || echo '$(srcdir)/'`arch/aarch64/cpu.c

arch/aarch64/libucs_la-global_opts.lo: arch/aarch64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/aarch64/libucs_la-global_opts.lo -MD -MP -MF arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Tpo -c -o arch/aarch64/libucs_la-global_opts.lo `test -f 'arch/aarch64/global_opts.c' || echo '$(srcdir)/'`arch/aarch64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Tpo arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/aarch64/global_opts.c' object='arch/aarch64/libucs_la-global_opts.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/aarch64/libucs_la-global_opts.lo `test -f 'arch/aarch64/global_opts.c' || echo '$(srcdir)/'`arch/aarch64/global_opts.c

arch/ppc64/libucs_la-timebase.lo: arch/ppc64/timebase.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/ppc64/libucs_la-timebase.lo -MD -MP -MF arch/ppc64/$(DEPDIR)/libucs_la-timebase.Tpo -c -o arch/ppc64/libucs_la-timebase.lo `test -f 'arch/ppc64/timebase.c' || echo '$(srcdir)/'`arch/ppc64/timebase.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/ppc64/$(DEPDIR)/libucs_la-timebase.Tpo arch/ppc64/$(DEPDIR)/libucs_la-timebase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/ppc64/timebase.c' object='arch/ppc64/libucs_la-timebase.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/ppc64/libucs_la-timebase.lo `test -f 'arch/ppc64/timebase.c' || echo '$(srcdir)/'`arch/ppc64/timebase.c

arch/ppc64/libucs_la-global_opts.lo: arch/ppc64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/ppc64/libucs_la-global_opts.lo -MD -MP -MF arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Tpo -c -o arch/ppc64/libucs_la-global_opts.lo `test -f 'arch/ppc64/global_opts.c' || echo '$(srcdir)/'`arch/ppc64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Tpo arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/ppc64/global_opts.c' object='arch/ppc64/libucs_la-global_opts.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/ppc64/libucs_la-global_opts.lo `test -f 'arch/ppc64/global_opts.c' || echo '$(srcdir)/'`arch/ppc64/global_opts.c

arch/x86_64/libucs_la-cpu.lo: arch/x86_64/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/x86_64/libucs_la-cpu.lo -MD -MP -MF arch/x86_64/$(DEPDIR)/libucs_la-cpu.Tpo -c -o arch/x86_64/libucs_la-cpu.lo `test -f 'arch/x86_64/cpu.c' || echo '$(srcdir)/'`arch/x86_64/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/x86_64/$(DEPDIR)/libucs_la-cpu.Tpo arch/x86_64/$(DEPDIR)/libucs_la-cpu.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/x86_64/cpu.c' object='arch/x86_64/libucs_la-cpu.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/x86_64/libucs_la-cpu.lo `test -f 'arch/x86_64/cpu.c' || echo '$(srcdir)/'`arch/x86_64/cpu.c

arch/x86_64/libucs_la-global_opts.lo: arch/x86_64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/x86_64/libucs_la-global_opts.lo -MD -MP -MF arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Tpo -c -o arch/x86_64/libucs_la-global_opts.lo `test -f 'arch/x86_64/global_opts.c' || echo '$(srcdir)/'`arch/x86_64/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Tpo arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/x86_64/global_opts.c' object='arch/x86_64/libucs_la-global_opts.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/x86_64/libucs_la-global_opts.lo `test -f 'arch/x86_64/global_opts.c' || echo '$(srcdir)/'`arch/x86_64/global_opts.c

arch/libucs_la-cpu.lo: arch/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT arch/libucs_la-cpu.lo -MD -MP -MF arch/$(DEPDIR)/libucs_la-cpu.Tpo -c -o arch/libucs_la-cpu.lo `test -f 'arch/cpu.c' || echo '$(srcdir)/'`arch/cpu.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) arch/$(DEPDIR)/libucs_la-cpu.Tpo arch/$(DEPDIR)/libucs_la-cpu.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='arch/cpu.c' object='arch/libucs_la-cpu.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o arch/libucs_la-cpu.lo `test -f 'arch/cpu.c' || echo '$(srcdir)/'`arch/cpu.c

async/libucs_la-async.lo: async/async.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT async/libucs_la-async.lo -MD -MP -MF async/$(DEPDIR)/libucs_la-async.Tpo -c -o async/libucs_la-async.lo `test -f 'async/async.c' || echo '$(srcdir)/'`async/async.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) async/$(DEPDIR)/libucs_la-async.Tpo async/$(DEPDIR)/libucs_la-async.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='async/async.c' object='async/libucs_la-async.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o async/libucs_la-async.lo `test -f 'async/async.c' || echo '$(srcdir)/'`async/async.c

async/libucs_la-signal.lo: async/signal.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT async/libucs_la-signal.lo -MD -MP -MF async/$(DEPDIR)/libucs_la-signal.Tpo -c -o async/libucs_la-signal.lo `test -f 'async/signal.c' || echo '$(srcdir)/'`async/signal.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) async/$(DEPDIR)/libucs_la-signal.Tpo async/$(DEPDIR)/libucs_la-signal.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='async/signal.c' object='async/libucs_la-signal.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o async/libucs_la-signal.lo `test -f 'async/signal.c' || echo '$(srcdir)/'`async/signal.c

async/libucs_la-pipe.lo: async/pipe.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT async/libucs_la-pipe.lo -MD -MP -MF async/$(DEPDIR)/libucs_la-pipe.Tpo -c -o async/libucs_la-pipe.lo `test -f 'async/pipe.c' || echo '$(srcdir)/'`async/pipe.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) async/$(DEPDIR)/libucs_la-pipe.Tpo async/$(DEPDIR)/libucs_la-pipe.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='async/pipe.c' object='async/libucs_la-pipe.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o async/libucs_la-pipe.lo `test -f 'async/pipe.c' || echo '$(srcdir)/'`async/pipe.c

async/libucs_la-thread.lo: async/thread.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT async/libucs_la-thread.lo -MD -MP -MF async/$(DEPDIR)/libucs_la-thread.Tpo -c -o async/libucs_la-thread.lo `test -f 'async/thread.c' || echo '$(srcdir)/'`async/thread.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) async/$(DEPDIR)/libucs_la-thread.Tpo async/$(DEPDIR)/libucs_la-thread.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='async/thread.c' object='async/libucs_la-thread.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o async/libucs_la-thread.lo `test -f 'async/thread.c' || echo '$(srcdir)/'`async/thread.c

config/libucs_la-global_opts.lo: config/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT config/libucs_la-global_opts.lo -MD -MP -MF config/$(DEPDIR)/libucs_la-global_opts.Tpo -c -o config/libucs_la-global_opts.lo `test -f 'config/global_opts.c' || echo '$(srcdir)/'`config/global_opts.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) config/$(DEPDIR)/libucs_la-global_opts.Tpo config/$(DEPDIR)/libucs_la-global_opts.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='config/global_opts.c' object='config/libucs_la-global_opts.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o config/libucs_la-global_opts.lo `test -f 'config/global_opts.c' || echo '$(srcdir)/'`config/global_opts.c

config/libucs_la-ucm_opts.lo: config/ucm_opts.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT config/libucs_la-ucm_opts.lo -MD -MP -MF config/$(DEPDIR)/libucs_la-ucm_opts.Tpo -c -o config/libucs_la-ucm_opts.lo `test -f 'config/ucm_opts.c' || echo '$(srcdir)/'`config/ucm_opts.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) config/$(DEPDIR)/libucs_la-ucm_opts.Tpo config/$(DEPDIR)/libucs_la-ucm_opts.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='config/ucm_opts.c' object='config/libucs_la-ucm_opts.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o config/libucs_la-ucm_opts.lo `test -f 'config/ucm_opts.c' || echo '$(srcdir)/'`config/ucm_opts.c

config/libucs_la-ini.lo: config/ini.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT config/libucs_la-ini.lo -MD -MP -MF config/$(DEPDIR)/libucs_la-ini.Tpo -c -o config/libucs_la-ini.lo `test -f 'config/ini.c' || echo '$(srcdir)/'`config/ini.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) config/$(DEPDIR)/libucs_la-ini.Tpo config/$(DEPDIR)/libucs_la-ini.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='config/ini.c' object='config/libucs_la-ini.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o config/libucs_la-ini.lo `test -f 'config/ini.c' || echo '$(srcdir)/'`config/ini.c

config/libucs_la-parser.lo: config/parser.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT config/libucs_la-parser.lo -MD -MP -MF config/$(DEPDIR)/libucs_la-parser.Tpo -c -o config/libucs_la-parser.lo `test -f 'config/parser.c' || echo '$(srcdir)/'`config/parser.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) config/$(DEPDIR)/libucs_la-parser.Tpo config/$(DEPDIR)/libucs_la-parser.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='config/parser.c' object='config/libucs_la-parser.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o config/libucs_la-parser.lo `test -f 'config/parser.c' || echo '$(srcdir)/'`config/parser.c

datastruct/libucs_la-arbiter.lo: datastruct/arbiter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-arbiter.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-arbiter.Tpo -c -o datastruct/libucs_la-arbiter.lo `test -f 'datastruct/arbiter.c' || echo '$(srcdir)/'`datastruct/arbiter.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-arbiter.Tpo datastruct/$(DEPDIR)/libucs_la-arbiter.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/arbiter.c' object='datastruct/libucs_la-arbiter.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-arbiter.lo `test -f 'datastruct/arbiter.c' || echo '$(srcdir)/'`datastruct/arbiter.c

datastruct/libucs_la-array.lo: datastruct/array.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-array.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-array.Tpo -c -o datastruct/libucs_la-array.lo `test -f 'datastruct/array.c' || echo '$(srcdir)/'`datastruct/array.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-array.Tpo datastruct/$(DEPDIR)/libucs_la-array.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/array.c' object='datastruct/libucs_la-array.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-array.lo `test -f 'datastruct/array.c' || echo '$(srcdir)/'`datastruct/array.c

datastruct/libucs_la-callbackq.lo: datastruct/callbackq.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-callbackq.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-callbackq.Tpo -c -o datastruct/libucs_la-callbackq.lo `test -f 'datastruct/callbackq.c' || echo '$(srcdir)/'`datastruct/callbackq.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-callbackq.Tpo datastruct/$(DEPDIR)/libucs_la-callbackq.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/callbackq.c' object='datastruct/libucs_la-callbackq.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-callbackq.lo `test -f 'datastruct/callbackq.c' || echo '$(srcdir)/'`datastruct/callbackq.c

datastruct/libucs_la-frag_list.lo: datastruct/frag_list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-frag_list.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-frag_list.Tpo -c -o datastruct/libucs_la-frag_list.lo `test -f 'datastruct/frag_list.c' || echo '$(srcdir)/'`datastruct/frag_list.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-frag_list.Tpo datastruct/$(DEPDIR)/libucs_la-frag_list.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/frag_list.c' object='datastruct/libucs_la-frag_list.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-frag_list.lo `test -f 'datastruct/frag_list.c' || echo '$(srcdir)/'`datastruct/frag_list.c

datastruct/libucs_la-mpmc.lo: datastruct/mpmc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-mpmc.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-mpmc.Tpo -c -o datastruct/libucs_la-mpmc.lo `test -f 'datastruct/mpmc.c' || echo '$(srcdir)/'`datastruct/mpmc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-mpmc.Tpo datastruct/$(DEPDIR)/libucs_la-mpmc.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/mpmc.c' object='datastruct/libucs_la-mpmc.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-mpmc.lo `test -f 'datastruct/mpmc.c' || echo '$(srcdir)/'`datastruct/mpmc.c

datastruct/libucs_la-mpool.lo: datastruct/mpool.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-mpool.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-mpool.Tpo -c -o datastruct/libucs_la-mpool.lo `test -f 'datastruct/mpool.c' || echo '$(srcdir)/'`datastruct/mpool.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-mpool.Tpo datastruct/$(DEPDIR)/libucs_la-mpool.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/mpool.c' object='datastruct/libucs_la-mpool.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-mpool.lo `test -f 'datastruct/mpool.c' || echo '$(srcdir)/'`datastruct/mpool.c

datastruct/libucs_la-mpool_set.lo: datastruct/mpool_set.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-mpool_set.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-mpool_set.Tpo -c -o datastruct/libucs_la-mpool_set.lo `test -f 'datastruct/mpool_set.c' || echo '$(srcdir)/'`datastruct/mpool_set.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-mpool_set.Tpo datastruct/$(DEPDIR)/libucs_la-mpool_set.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/mpool_set.c' object='datastruct/libucs_la-mpool_set.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-mpool_set.lo `test -f 'datastruct/mpool_set.c' || echo '$(srcdir)/'`datastruct/mpool_set.c

datastruct/libucs_la-pgtable.lo: datastruct/pgtable.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-pgtable.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-pgtable.Tpo -c -o datastruct/libucs_la-pgtable.lo `test -f 'datastruct/pgtable.c' || echo '$(srcdir)/'`datastruct/pgtable.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-pgtable.Tpo datastruct/$(DEPDIR)/libucs_la-pgtable.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/pgtable.c' object='datastruct/libucs_la-pgtable.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-pgtable.lo `test -f 'datastruct/pgtable.c' || echo '$(srcdir)/'`datastruct/pgtable.c

datastruct/libucs_la-ptr_array.lo: datastruct/ptr_array.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-ptr_array.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-ptr_array.Tpo -c -o datastruct/libucs_la-ptr_array.lo `test -f 'datastruct/ptr_array.c' || echo '$(srcdir)/'`datastruct/ptr_array.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-ptr_array.Tpo datastruct/$(DEPDIR)/libucs_la-ptr_array.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/ptr_array.c' object='datastruct/libucs_la-ptr_array.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-ptr_array.lo `test -f 'datastruct/ptr_array.c' || echo '$(srcdir)/'`datastruct/ptr_array.c

datastruct/libucs_la-ptr_map.lo: datastruct/ptr_map.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-ptr_map.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-ptr_map.Tpo -c -o datastruct/libucs_la-ptr_map.lo `test -f 'datastruct/ptr_map.c' || echo '$(srcdir)/'`datastruct/ptr_map.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-ptr_map.Tpo datastruct/$(DEPDIR)/libucs_la-ptr_map.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/ptr_map.c' object='datastruct/libucs_la-ptr_map.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-ptr_map.lo `test -f 'datastruct/ptr_map.c' || echo '$(srcdir)/'`datastruct/ptr_map.c

datastruct/libucs_la-strided_alloc.lo: datastruct/strided_alloc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-strided_alloc.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-strided_alloc.Tpo -c -o datastruct/libucs_la-strided_alloc.lo `test -f 'datastruct/strided_alloc.c' || echo '$(srcdir)/'`datastruct/strided_alloc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-strided_alloc.Tpo datastruct/$(DEPDIR)/libucs_la-strided_alloc.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/strided_alloc.c' object='datastruct/libucs_la-strided_alloc.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-strided_alloc.lo `test -f 'datastruct/strided_alloc.c' || echo '$(srcdir)/'`datastruct/strided_alloc.c

datastruct/libucs_la-string_buffer.lo: datastruct/string_buffer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-string_buffer.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-string_buffer.Tpo -c -o datastruct/libucs_la-string_buffer.lo `test -f 'datastruct/string_buffer.c' || echo '$(srcdir)/'`datastruct/string_buffer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-string_buffer.Tpo datastruct/$(DEPDIR)/libucs_la-string_buffer.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/string_buffer.c' object='datastruct/libucs_la-string_buffer.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-string_buffer.lo `test -f 'datastruct/string_buffer.c' || echo '$(srcdir)/'`datastruct/string_buffer.c

datastruct/libucs_la-string_set.lo: datastruct/string_set.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-string_set.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-string_set.Tpo -c -o datastruct/libucs_la-string_set.lo `test -f 'datastruct/string_set.c' || echo '$(srcdir)/'`datastruct/string_set.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-string_set.Tpo datastruct/$(DEPDIR)/libucs_la-string_set.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/string_set.c' object='datastruct/libucs_la-string_set.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-string_set.lo `test -f 'datastruct/string_set.c' || echo '$(srcdir)/'`datastruct/string_set.c

datastruct/libucs_la-conn_match.lo: datastruct/conn_match.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT datastruct/libucs_la-conn_match.lo -MD -MP -MF datastruct/$(DEPDIR)/libucs_la-conn_match.Tpo -c -o datastruct/libucs_la-conn_match.lo `test -f 'datastruct/conn_match.c' || echo '$(srcdir)/'`datastruct/conn_match.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) datastruct/$(DEPDIR)/libucs_la-conn_match.Tpo datastruct/$(DEPDIR)/libucs_la-conn_match.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datastruct/conn_match.c' object='datastruct/libucs_la-conn_match.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o datastruct/libucs_la-conn_match.lo `test -f 'datastruct/conn_match.c' || echo '$(srcdir)/'`datastruct/conn_match.c

debug/libucs_la-assert.lo: debug/assert.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT debug/libucs_la-assert.lo -MD -MP -MF debug/$(DEPDIR)/libucs_la-assert.Tpo -c -o debug/libucs_la-assert.lo `test -f 'debug/assert.c' || echo '$(srcdir)/'`debug/assert.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) debug/$(DEPDIR)/libucs_la-assert.Tpo debug/$(DEPDIR)/libucs_la-assert.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='debug/assert.c' object='debug/libucs_la-assert.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o debug/libucs_la-assert.lo `test -f 'debug/assert.c' || echo '$(srcdir)/'`debug/assert.c

debug/libucs_la-debug.lo: debug/debug.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT debug/libucs_la-debug.lo -MD -MP -MF debug/$(DEPDIR)/libucs_la-debug.Tpo -c -o debug/libucs_la-debug.lo `test -f 'debug/debug.c' || echo '$(srcdir)/'`debug/debug.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) debug/$(DEPDIR)/libucs_la-debug.Tpo debug/$(DEPDIR)/libucs_la-debug.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='debug/debug.c' object='debug/libucs_la-debug.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o debug/libucs_la-debug.lo `test -f 'debug/debug.c' || echo '$(srcdir)/'`debug/debug.c

debug/libucs_la-log.lo: debug/log.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT debug/libucs_la-log.lo -MD -MP -MF debug/$(DEPDIR)/libucs_la-log.Tpo -c -o debug/libucs_la-log.lo `test -f 'debug/log.c' || echo '$(srcdir)/'`debug/log.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) debug/$(DEPDIR)/libucs_la-log.Tpo debug/$(DEPDIR)/libucs_la-log.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='debug/log.c' object='debug/libucs_la-log.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o debug/libucs_la-log.lo `test -f 'debug/log.c' || echo '$(srcdir)/'`debug/log.c

debug/libucs_la-memtrack.lo: debug/memtrack.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT debug/libucs_la-memtrack.lo -MD -MP -MF debug/$(DEPDIR)/libucs_la-memtrack.Tpo -c -o debug/libucs_la-memtrack.lo `test -f 'debug/memtrack.c' || echo '$(srcdir)/'`debug/memtrack.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) debug/$(DEPDIR)/libucs_la-memtrack.Tpo debug/$(DEPDIR)/libucs_la-memtrack.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='debug/memtrack.c' object='debug/libucs_la-memtrack.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o debug/libucs_la-memtrack.lo `test -f 'debug/memtrack.c' || echo '$(srcdir)/'`debug/memtrack.c

memory/libucs_la-memory_type.lo: memory/memory_type.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT memory/libucs_la-memory_type.lo -MD -MP -MF memory/$(DEPDIR)/libucs_la-memory_type.Tpo -c -o memory/libucs_la-memory_type.lo `test -f 'memory/memory_type.c' || echo '$(srcdir)/'`memory/memory_type.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) memory/$(DEPDIR)/libucs_la-memory_type.Tpo memory/$(DEPDIR)/libucs_la-memory_type.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='memory/memory_type.c' object='memory/libucs_la-memory_type.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o memory/libucs_la-memory_type.lo `test -f 'memory/memory_type.c' || echo '$(srcdir)/'`memory/memory_type.c

memory/libucs_la-memtype_cache.lo: memory/memtype_cache.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT memory/libucs_la-memtype_cache.lo -MD -MP -MF memory/$(DEPDIR)/libucs_la-memtype_cache.Tpo -c -o memory/libucs_la-memtype_cache.lo `test -f 'memory/memtype_cache.c' || echo '$(srcdir)/'`memory/memtype_cache.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) memory/$(DEPDIR)/libucs_la-memtype_cache.Tpo memory/$(DEPDIR)/libucs_la-memtype_cache.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='memory/memtype_cache.c' object='memory/libucs_la-memtype_cache.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o memory/libucs_la-memtype_cache.lo `test -f 'memory/memtype_cache.c' || echo '$(srcdir)/'`memory/memtype_cache.c

memory/libucs_la-numa.lo: memory/numa.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT memory/libucs_la-numa.lo -MD -MP -MF memory/$(DEPDIR)/libucs_la-numa.Tpo -c -o memory/libucs_la-numa.lo `test -f 'memory/numa.c' || echo '$(srcdir)/'`memory/numa.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) memory/$(DEPDIR)/libucs_la-numa.Tpo memory/$(DEPDIR)/libucs_la-numa.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='memory/numa.c' object='memory/libucs_la-numa.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o memory/libucs_la-numa.lo `test -f 'memory/numa.c' || echo '$(srcdir)/'`memory/numa.c

memory/libucs_la-rcache.lo: memory/rcache.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT memory/libucs_la-rcache.lo -MD -MP -MF memory/$(DEPDIR)/libucs_la-rcache.Tpo -c -o memory/libucs_la-rcache.lo `test -f 'memory/rcache.c' || echo '$(srcdir)/'`memory/rcache.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) memory/$(DEPDIR)/libucs_la-rcache.Tpo memory/$(DEPDIR)/libucs_la-rcache.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='memory/rcache.c' object='memory/libucs_la-rcache.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o memory/libucs_la-rcache.lo `test -f 'memory/rcache.c' || echo '$(srcdir)/'`memory/rcache.c

memory/libucs_la-rcache_vfs.lo: memory/rcache_vfs.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT memory/libucs_la-rcache_vfs.lo -MD -MP -MF memory/$(DEPDIR)/libucs_la-rcache_vfs.Tpo -c -o memory/libucs_la-rcache_vfs.lo `test -f 'memory/rcache_vfs.c' || echo '$(srcdir)/'`memory/rcache_vfs.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) memory/$(DEPDIR)/libucs_la-rcache_vfs.Tpo memory/$(DEPDIR)/libucs_la-rcache_vfs.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='memory/rcache_vfs.c' object='memory/libucs_la-rcache_vfs.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o memory/libucs_la-rcache_vfs.lo `test -f 'memory/rcache_vfs.c' || echo '$(srcdir)/'`memory/rcache_vfs.c

profile/libucs_la-profile.lo: profile/profile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT profile/libucs_la-profile.lo -MD -MP -MF profile/$(DEPDIR)/libucs_la-profile.Tpo -c -o profile/libucs_la-profile.lo `test -f 'profile/profile.c' || echo '$(srcdir)/'`profile/profile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) profile/$(DEPDIR)/libucs_la-profile.Tpo profile/$(DEPDIR)/libucs_la-profile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='profile/profile.c' object='profile/libucs_la-profile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o profile/libucs_la-profile.lo `test -f 'profile/profile.c' || echo '$(srcdir)/'`profile/profile.c

stats/libucs_la-stats.lo: stats/stats.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT stats/libucs_la-stats.lo -MD -MP -MF stats/$(DEPDIR)/libucs_la-stats.Tpo -c -o stats/libucs_la-stats.lo `test -f 'stats/stats.c' || echo '$(srcdir)/'`stats/stats.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/libucs_la-stats.Tpo stats/$(DEPDIR)/libucs_la-stats.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/stats.c' object='stats/libucs_la-stats.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o stats/libucs_la-stats.lo `test -f 'stats/stats.c' || echo '$(srcdir)/'`stats/stats.c

sys/libucs_la-event_set.lo: sys/event_set.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-event_set.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-event_set.Tpo -c -o sys/libucs_la-event_set.lo `test -f 'sys/event_set.c' || echo '$(srcdir)/'`sys/event_set.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-event_set.Tpo sys/$(DEPDIR)/libucs_la-event_set.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/event_set.c' object='sys/libucs_la-event_set.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-event_set.lo `test -f 'sys/event_set.c' || echo '$(srcdir)/'`sys/event_set.c

sys/libucs_la-init.lo: sys/init.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-init.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-init.Tpo -c -o sys/libucs_la-init.lo `test -f 'sys/init.c' || echo '$(srcdir)/'`sys/init.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-init.Tpo sys/$(DEPDIR)/libucs_la-init.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/init.c' object='sys/libucs_la-init.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-init.lo `test -f 'sys/init.c' || echo '$(srcdir)/'`sys/init.c

sys/libucs_la-math.lo: sys/math.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-math.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-math.Tpo -c -o sys/libucs_la-math.lo `test -f 'sys/math.c' || echo '$(srcdir)/'`sys/math.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-math.Tpo sys/$(DEPDIR)/libucs_la-math.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/math.c' object='sys/libucs_la-math.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-math.lo `test -f 'sys/math.c' || echo '$(srcdir)/'`sys/math.c

sys/libucs_la-module.lo: sys/module.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-module.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-module.Tpo -c -o sys/libucs_la-module.lo `test -f 'sys/module.c' || echo '$(srcdir)/'`sys/module.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-module.Tpo sys/$(DEPDIR)/libucs_la-module.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/module.c' object='sys/libucs_la-module.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-module.lo `test -f 'sys/module.c' || echo '$(srcdir)/'`sys/module.c

sys/libucs_la-string.lo: sys/string.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-string.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-string.Tpo -c -o sys/libucs_la-string.lo `test -f 'sys/string.c' || echo '$(srcdir)/'`sys/string.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-string.Tpo sys/$(DEPDIR)/libucs_la-string.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/string.c' object='sys/libucs_la-string.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-string.lo `test -f 'sys/string.c' || echo '$(srcdir)/'`sys/string.c

sys/libucs_la-sys.lo: sys/sys.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-sys.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-sys.Tpo -c -o sys/libucs_la-sys.lo `test -f 'sys/sys.c' || echo '$(srcdir)/'`sys/sys.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-sys.Tpo sys/$(DEPDIR)/libucs_la-sys.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/sys.c' object='sys/libucs_la-sys.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-sys.lo `test -f 'sys/sys.c' || echo '$(srcdir)/'`sys/sys.c

sys/libucs_la-iovec.lo: sys/iovec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-iovec.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-iovec.Tpo -c -o sys/libucs_la-iovec.lo `test -f 'sys/iovec.c' || echo '$(srcdir)/'`sys/iovec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-iovec.Tpo sys/$(DEPDIR)/libucs_la-iovec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/iovec.c' object='sys/libucs_la-iovec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-iovec.lo `test -f 'sys/iovec.c' || echo '$(srcdir)/'`sys/iovec.c

sys/libucs_la-lib.lo: sys/lib.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-lib.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-lib.Tpo -c -o sys/libucs_la-lib.lo `test -f 'sys/lib.c' || echo '$(srcdir)/'`sys/lib.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-lib.Tpo sys/$(DEPDIR)/libucs_la-lib.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/lib.c' object='sys/libucs_la-lib.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-lib.lo `test -f 'sys/lib.c' || echo '$(srcdir)/'`sys/lib.c

sys/libucs_la-sock.lo: sys/sock.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-sock.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-sock.Tpo -c -o sys/libucs_la-sock.lo `test -f 'sys/sock.c' || echo '$(srcdir)/'`sys/sock.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-sock.Tpo sys/$(DEPDIR)/libucs_la-sock.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/sock.c' object='sys/libucs_la-sock.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-sock.lo `test -f 'sys/sock.c' || echo '$(srcdir)/'`sys/sock.c

sys/topo/base/libucs_la-topo.lo: sys/topo/base/topo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/topo/base/libucs_la-topo.lo -MD -MP -MF sys/topo/base/$(DEPDIR)/libucs_la-topo.Tpo -c -o sys/topo/base/libucs_la-topo.lo `test -f 'sys/topo/base/topo.c' || echo '$(srcdir)/'`sys/topo/base/topo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/topo/base/$(DEPDIR)/libucs_la-topo.Tpo sys/topo/base/$(DEPDIR)/libucs_la-topo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/topo/base/topo.c' object='sys/topo/base/libucs_la-topo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/topo/base/libucs_la-topo.lo `test -f 'sys/topo/base/topo.c' || echo '$(srcdir)/'`sys/topo/base/topo.c

sys/libucs_la-stubs.lo: sys/stubs.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-stubs.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-stubs.Tpo -c -o sys/libucs_la-stubs.lo `test -f 'sys/stubs.c' || echo '$(srcdir)/'`sys/stubs.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-stubs.Tpo sys/$(DEPDIR)/libucs_la-stubs.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/stubs.c' object='sys/libucs_la-stubs.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-stubs.lo `test -f 'sys/stubs.c' || echo '$(srcdir)/'`sys/stubs.c

sys/libucs_la-uid.lo: sys/uid.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT sys/libucs_la-uid.lo -MD -MP -MF sys/$(DEPDIR)/libucs_la-uid.Tpo -c -o sys/libucs_la-uid.lo `test -f 'sys/uid.c' || echo '$(srcdir)/'`sys/uid.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sys/$(DEPDIR)/libucs_la-uid.Tpo sys/$(DEPDIR)/libucs_la-uid.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sys/uid.c' object='sys/libucs_la-uid.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o sys/libucs_la-uid.lo `test -f 'sys/uid.c' || echo '$(srcdir)/'`sys/uid.c

time/libucs_la-time.lo: time/time.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT time/libucs_la-time.lo -MD -MP -MF time/$(DEPDIR)/libucs_la-time.Tpo -c -o time/libucs_la-time.lo `test -f 'time/time.c' || echo '$(srcdir)/'`time/time.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) time/$(DEPDIR)/libucs_la-time.Tpo time/$(DEPDIR)/libucs_la-time.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='time/time.c' object='time/libucs_la-time.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o time/libucs_la-time.lo `test -f 'time/time.c' || echo '$(srcdir)/'`time/time.c

time/libucs_la-timer_wheel.lo: time/timer_wheel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT time/libucs_la-timer_wheel.lo -MD -MP -MF time/$(DEPDIR)/libucs_la-timer_wheel.Tpo -c -o time/libucs_la-timer_wheel.lo `test -f 'time/timer_wheel.c' || echo '$(srcdir)/'`time/timer_wheel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) time/$(DEPDIR)/libucs_la-timer_wheel.Tpo time/$(DEPDIR)/libucs_la-timer_wheel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='time/timer_wheel.c' object='time/libucs_la-timer_wheel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o time/libucs_la-timer_wheel.lo `test -f 'time/timer_wheel.c' || echo '$(srcdir)/'`time/timer_wheel.c

time/libucs_la-timerq.lo: time/timerq.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT time/libucs_la-timerq.lo -MD -MP -MF time/$(DEPDIR)/libucs_la-timerq.Tpo -c -o time/libucs_la-timerq.lo `test -f 'time/timerq.c' || echo '$(srcdir)/'`time/timerq.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) time/$(DEPDIR)/libucs_la-timerq.Tpo time/$(DEPDIR)/libucs_la-timerq.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='time/timerq.c' object='time/libucs_la-timerq.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o time/libucs_la-timerq.lo `test -f 'time/timerq.c' || echo '$(srcdir)/'`time/timerq.c

type/libucs_la-class.lo: type/class.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT type/libucs_la-class.lo -MD -MP -MF type/$(DEPDIR)/libucs_la-class.Tpo -c -o type/libucs_la-class.lo `test -f 'type/class.c' || echo '$(srcdir)/'`type/class.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) type/$(DEPDIR)/libucs_la-class.Tpo type/$(DEPDIR)/libucs_la-class.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='type/class.c' object='type/libucs_la-class.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o type/libucs_la-class.lo `test -f 'type/class.c' || echo '$(srcdir)/'`type/class.c

type/libucs_la-status.lo: type/status.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT type/libucs_la-status.lo -MD -MP -MF type/$(DEPDIR)/libucs_la-status.Tpo -c -o type/libucs_la-status.lo `test -f 'type/status.c' || echo '$(srcdir)/'`type/status.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) type/$(DEPDIR)/libucs_la-status.Tpo type/$(DEPDIR)/libucs_la-status.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='type/status.c' object='type/libucs_la-status.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o type/libucs_la-status.lo `test -f 'type/status.c' || echo '$(srcdir)/'`type/status.c

type/libucs_la-spinlock.lo: type/spinlock.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT type/libucs_la-spinlock.lo -MD -MP -MF type/$(DEPDIR)/libucs_la-spinlock.Tpo -c -o type/libucs_la-spinlock.lo `test -f 'type/spinlock.c' || echo '$(srcdir)/'`type/spinlock.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) type/$(DEPDIR)/libucs_la-spinlock.Tpo type/$(DEPDIR)/libucs_la-spinlock.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='type/spinlock.c' object='type/libucs_la-spinlock.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o type/libucs_la-spinlock.lo `test -f 'type/spinlock.c' || echo '$(srcdir)/'`type/spinlock.c

type/libucs_la-thread_mode.lo: type/thread_mode.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT type/libucs_la-thread_mode.lo -MD -MP -MF type/$(DEPDIR)/libucs_la-thread_mode.Tpo -c -o type/libucs_la-thread_mode.lo `test -f 'type/thread_mode.c' || echo '$(srcdir)/'`type/thread_mode.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) type/$(DEPDIR)/libucs_la-thread_mode.Tpo type/$(DEPDIR)/libucs_la-thread_mode.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='type/thread_mode.c' object='type/libucs_la-thread_mode.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o type/libucs_la-thread_mode.lo `test -f 'type/thread_mode.c' || echo '$(srcdir)/'`type/thread_mode.c

vfs/base/libucs_la-vfs_obj.lo: vfs/base/vfs_obj.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT vfs/base/libucs_la-vfs_obj.lo -MD -MP -MF vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Tpo -c -o vfs/base/libucs_la-vfs_obj.lo `test -f 'vfs/base/vfs_obj.c' || echo '$(srcdir)/'`vfs/base/vfs_obj.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Tpo vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='vfs/base/vfs_obj.c' object='vfs/base/libucs_la-vfs_obj.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o vfs/base/libucs_la-vfs_obj.lo `test -f 'vfs/base/vfs_obj.c' || echo '$(srcdir)/'`vfs/base/vfs_obj.c

vfs/base/libucs_la-vfs_cb.lo: vfs/base/vfs_cb.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT vfs/base/libucs_la-vfs_cb.lo -MD -MP -MF vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Tpo -c -o vfs/base/libucs_la-vfs_cb.lo `test -f 'vfs/base/vfs_cb.c' || echo '$(srcdir)/'`vfs/base/vfs_cb.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Tpo vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='vfs/base/vfs_cb.c' object='vfs/base/libucs_la-vfs_cb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o vfs/base/libucs_la-vfs_cb.lo `test -f 'vfs/base/vfs_cb.c' || echo '$(srcdir)/'`vfs/base/vfs_cb.c

stats/libucs_la-client_server.lo: stats/client_server.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT stats/libucs_la-client_server.lo -MD -MP -MF stats/$(DEPDIR)/libucs_la-client_server.Tpo -c -o stats/libucs_la-client_server.lo `test -f 'stats/client_server.c' || echo '$(srcdir)/'`stats/client_server.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/libucs_la-client_server.Tpo stats/$(DEPDIR)/libucs_la-client_server.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/client_server.c' object='stats/libucs_la-client_server.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o stats/libucs_la-client_server.lo `test -f 'stats/client_server.c' || echo '$(srcdir)/'`stats/client_server.c

stats/libucs_la-serialization.lo: stats/serialization.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT stats/libucs_la-serialization.lo -MD -MP -MF stats/$(DEPDIR)/libucs_la-serialization.Tpo -c -o stats/libucs_la-serialization.lo `test -f 'stats/serialization.c' || echo '$(srcdir)/'`stats/serialization.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/libucs_la-serialization.Tpo stats/$(DEPDIR)/libucs_la-serialization.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/serialization.c' object='stats/libucs_la-serialization.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o stats/libucs_la-serialization.lo `test -f 'stats/serialization.c' || echo '$(srcdir)/'`stats/serialization.c

stats/libucs_la-libstats.lo: stats/libstats.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -MT stats/libucs_la-libstats.lo -MD -MP -MF stats/$(DEPDIR)/libucs_la-libstats.Tpo -c -o stats/libucs_la-libstats.lo `test -f 'stats/libstats.c' || echo '$(srcdir)/'`stats/libstats.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/libucs_la-libstats.Tpo stats/$(DEPDIR)/libucs_la-libstats.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/libstats.c' object='stats/libucs_la-libstats.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libucs_la_CPPFLAGS) $(CPPFLAGS) $(libucs_la_CFLAGS) $(CFLAGS) -c -o stats/libucs_la-libstats.lo `test -f 'stats/libstats.c' || echo '$(srcdir)/'`stats/libstats.c

stats/ucs_stats_parser-stats_parser.o: stats/stats_parser.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ucs_stats_parser_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT stats/ucs_stats_parser-stats_parser.o -MD -MP -MF stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Tpo -c -o stats/ucs_stats_parser-stats_parser.o `test -f 'stats/stats_parser.c' || echo '$(srcdir)/'`stats/stats_parser.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Tpo stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/stats_parser.c' object='stats/ucs_stats_parser-stats_parser.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ucs_stats_parser_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o stats/ucs_stats_parser-stats_parser.o `test -f 'stats/stats_parser.c' || echo '$(srcdir)/'`stats/stats_parser.c

stats/ucs_stats_parser-stats_parser.obj: stats/stats_parser.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ucs_stats_parser_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT stats/ucs_stats_parser-stats_parser.obj -MD -MP -MF stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Tpo -c -o stats/ucs_stats_parser-stats_parser.obj `if test -f 'stats/stats_parser.c'; then $(CYGPATH_W) 'stats/stats_parser.c'; else $(CYGPATH_W) '$(srcdir)/stats/stats_parser.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Tpo stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='stats/stats_parser.c' object='stats/ucs_stats_parser-stats_parser.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ucs_stats_parser_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o stats/ucs_stats_parser-stats_parser.obj `if test -f 'stats/stats_parser.c'; then $(CYGPATH_W) 'stats/stats_parser.c'; else $(CYGPATH_W) '$(srcdir)/stats/stats_parser.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf algorithm/.libs algorithm/_libs
	-rm -rf arch/.libs arch/_libs
	-rm -rf arch/aarch64/.libs arch/aarch64/_libs
	-rm -rf arch/ppc64/.libs arch/ppc64/_libs
	-rm -rf arch/x86_64/.libs arch/x86_64/_libs
	-rm -rf async/.libs async/_libs
	-rm -rf config/.libs config/_libs
	-rm -rf datastruct/.libs datastruct/_libs
	-rm -rf debug/.libs debug/_libs
	-rm -rf memory/.libs memory/_libs
	-rm -rf profile/.libs profile/_libs
	-rm -rf stats/.libs stats/_libs
	-rm -rf sys/.libs sys/_libs
	-rm -rf sys/topo/base/.libs sys/topo/base/_libs
	-rm -rf time/.libs time/_libs
	-rm -rf type/.libs type/_libs
	-rm -rf vfs/base/.libs vfs/base/_libs
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-nobase_dist_libucs_laHEADERS: $(nobase_dist_libucs_la_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nobase_dist_libucs_la_HEADERS)'; test -n "$(libucs_ladir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libucs_ladir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libucs_ladir)" || exit 1; \
	fi; \
	$(am__nobase_list) | while read dir files; do \
	  xfiles=; for file in $$files; do \
	    if test -f "$$file"; then xfiles="$$xfiles $$file"; \
	    else xfiles="$$xfiles $(srcdir)/$$file"; fi; done; \
	  test -z "$$xfiles" || { \
	    test "x$$dir" = x. || { \
	      echo " $(MKDIR_P) '$(DESTDIR)$(libucs_ladir)/$$dir'"; \
	      $(MKDIR_P) "$(DESTDIR)$(libucs_ladir)/$$dir"; }; \
	    echo " $(INSTALL_HEADER) $$xfiles '$(DESTDIR)$(libucs_ladir)/$$dir'"; \
	    $(INSTALL_HEADER) $$xfiles "$(DESTDIR)$(libucs_ladir)/$$dir" || exit $$?; }; \
	done

uninstall-nobase_dist_libucs_laHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nobase_dist_libucs_la_HEADERS)'; test -n "$(libucs_ladir)" || list=; \
	$(am__nobase_strip_setup); files=`$(am__nobase_strip)`; \
	dir='$(DESTDIR)$(libucs_ladir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-recursive
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(DATA) $(HEADERS) \
		all-local
install-binPROGRAMS: install-libLTLIBRARIES

installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(libucs_ladir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f algorithm/$(DEPDIR)/$(am__dirstamp)
	-rm -f algorithm/$(am__dirstamp)
	-rm -f arch/$(DEPDIR)/$(am__dirstamp)
	-rm -f arch/$(am__dirstamp)
	-rm -f arch/aarch64/$(DEPDIR)/$(am__dirstamp)
	-rm -f arch/aarch64/$(am__dirstamp)
	-rm -f arch/ppc64/$(DEPDIR)/$(am__dirstamp)
	-rm -f arch/ppc64/$(am__dirstamp)
	-rm -f arch/x86_64/$(DEPDIR)/$(am__dirstamp)
	-rm -f arch/x86_64/$(am__dirstamp)
	-rm -f async/$(DEPDIR)/$(am__dirstamp)
	-rm -f async/$(am__dirstamp)
	-rm -f config/$(DEPDIR)/$(am__dirstamp)
	-rm -f config/$(am__dirstamp)
	-rm -f datastruct/$(DEPDIR)/$(am__dirstamp)
	-rm -f datastruct/$(am__dirstamp)
	-rm -f debug/$(DEPDIR)/$(am__dirstamp)
	-rm -f debug/$(am__dirstamp)
	-rm -f memory/$(DEPDIR)/$(am__dirstamp)
	-rm -f memory/$(am__dirstamp)
	-rm -f profile/$(DEPDIR)/$(am__dirstamp)
	-rm -f profile/$(am__dirstamp)
	-rm -f stats/$(DEPDIR)/$(am__dirstamp)
	-rm -f stats/$(am__dirstamp)
	-rm -f sys/$(DEPDIR)/$(am__dirstamp)
	-rm -f sys/$(am__dirstamp)
	-rm -f sys/topo/base/$(DEPDIR)/$(am__dirstamp)
	-rm -f sys/topo/base/$(am__dirstamp)
	-rm -f time/$(DEPDIR)/$(am__dirstamp)
	-rm -f time/$(am__dirstamp)
	-rm -f type/$(DEPDIR)/$(am__dirstamp)
	-rm -f type/$(am__dirstamp)
	-rm -f vfs/base/$(DEPDIR)/$(am__dirstamp)
	-rm -f vfs/base/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-binPROGRAMS clean-generic clean-libLTLIBRARIES \
	clean-libtool mostlyclean-am

distclean: distclean-recursive
		-rm -f algorithm/$(DEPDIR)/libucs_la-crc.Plo
	-rm -f algorithm/$(DEPDIR)/libucs_la-qsort_r.Plo
	-rm -f arch/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Plo
	-rm -f arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f arch/ppc64/$(DEPDIR)/libucs_la-timebase.Plo
	-rm -f arch/x86_64/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f async/$(DEPDIR)/libucs_la-async.Plo
	-rm -f async/$(DEPDIR)/libucs_la-pipe.Plo
	-rm -f async/$(DEPDIR)/libucs_la-signal.Plo
	-rm -f async/$(DEPDIR)/libucs_la-thread.Plo
	-rm -f config/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f config/$(DEPDIR)/libucs_la-ini.Plo
	-rm -f config/$(DEPDIR)/libucs_la-parser.Plo
	-rm -f config/$(DEPDIR)/libucs_la-ucm_opts.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-arbiter.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-array.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-callbackq.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-conn_match.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-frag_list.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpmc.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpool.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpool_set.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-pgtable.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-ptr_array.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-ptr_map.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-strided_alloc.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-string_buffer.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-string_set.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-assert.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-debug.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-log.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-memtrack.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-memory_type.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-memtype_cache.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-numa.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-rcache.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-rcache_vfs.Plo
	-rm -f profile/$(DEPDIR)/libucs_la-profile.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-client_server.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-libstats.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-serialization.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-stats.Plo
	-rm -f stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po
	-rm -f sys/$(DEPDIR)/libucs_la-event_set.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-init.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-iovec.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-lib.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-math.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-module.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-sock.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-string.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-stubs.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-sys.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-uid.Plo
	-rm -f sys/topo/base/$(DEPDIR)/libucs_la-topo.Plo
	-rm -f time/$(DEPDIR)/libucs_la-time.Plo
	-rm -f time/$(DEPDIR)/libucs_la-timer_wheel.Plo
	-rm -f time/$(DEPDIR)/libucs_la-timerq.Plo
	-rm -f type/$(DEPDIR)/libucs_la-class.Plo
	-rm -f type/$(DEPDIR)/libucs_la-spinlock.Plo
	-rm -f type/$(DEPDIR)/libucs_la-status.Plo
	-rm -f type/$(DEPDIR)/libucs_la-thread_mode.Plo
	-rm -f vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Plo
	-rm -f vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-nobase_dist_libucs_laHEADERS \
	install-pkgconfigDATA

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-binPROGRAMS install-libLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f algorithm/$(DEPDIR)/libucs_la-crc.Plo
	-rm -f algorithm/$(DEPDIR)/libucs_la-qsort_r.Plo
	-rm -f arch/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f arch/aarch64/$(DEPDIR)/libucs_la-memcpy_thunderx2.Plo
	-rm -f arch/ppc64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f arch/ppc64/$(DEPDIR)/libucs_la-timebase.Plo
	-rm -f arch/x86_64/$(DEPDIR)/libucs_la-cpu.Plo
	-rm -f arch/x86_64/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f async/$(DEPDIR)/libucs_la-async.Plo
	-rm -f async/$(DEPDIR)/libucs_la-pipe.Plo
	-rm -f async/$(DEPDIR)/libucs_la-signal.Plo
	-rm -f async/$(DEPDIR)/libucs_la-thread.Plo
	-rm -f config/$(DEPDIR)/libucs_la-global_opts.Plo
	-rm -f config/$(DEPDIR)/libucs_la-ini.Plo
	-rm -f config/$(DEPDIR)/libucs_la-parser.Plo
	-rm -f config/$(DEPDIR)/libucs_la-ucm_opts.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-arbiter.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-array.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-callbackq.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-conn_match.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-frag_list.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpmc.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpool.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-mpool_set.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-pgtable.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-ptr_array.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-ptr_map.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-strided_alloc.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-string_buffer.Plo
	-rm -f datastruct/$(DEPDIR)/libucs_la-string_set.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-assert.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-debug.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-log.Plo
	-rm -f debug/$(DEPDIR)/libucs_la-memtrack.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-memory_type.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-memtype_cache.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-numa.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-rcache.Plo
	-rm -f memory/$(DEPDIR)/libucs_la-rcache_vfs.Plo
	-rm -f profile/$(DEPDIR)/libucs_la-profile.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-client_server.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-libstats.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-serialization.Plo
	-rm -f stats/$(DEPDIR)/libucs_la-stats.Plo
	-rm -f stats/$(DEPDIR)/ucs_stats_parser-stats_parser.Po
	-rm -f sys/$(DEPDIR)/libucs_la-event_set.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-init.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-iovec.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-lib.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-math.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-module.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-sock.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-string.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-stubs.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-sys.Plo
	-rm -f sys/$(DEPDIR)/libucs_la-uid.Plo
	-rm -f sys/topo/base/$(DEPDIR)/libucs_la-topo.Plo
	-rm -f time/$(DEPDIR)/libucs_la-time.Plo
	-rm -f time/$(DEPDIR)/libucs_la-timer_wheel.Plo
	-rm -f time/$(DEPDIR)/libucs_la-timerq.Plo
	-rm -f type/$(DEPDIR)/libucs_la-class.Plo
	-rm -f type/$(DEPDIR)/libucs_la-spinlock.Plo
	-rm -f type/$(DEPDIR)/libucs_la-status.Plo
	-rm -f type/$(DEPDIR)/libucs_la-thread_mode.Plo
	-rm -f vfs/base/$(DEPDIR)/libucs_la-vfs_cb.Plo
	-rm -f vfs/base/$(DEPDIR)/libucs_la-vfs_obj.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-libLTLIBRARIES \
	uninstall-nobase_dist_libucs_laHEADERS uninstall-pkgconfigDATA

.MAKE: $(am__recursive_targets) install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am all-local \
	am--depfiles check check-am clean clean-binPROGRAMS \
	clean-generic clean-libLTLIBRARIES clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-libLTLIBRARIES \
	install-man install-nobase_dist_libucs_laHEADERS install-pdf \
	install-pdf-am install-pkgconfigDATA install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	installdirs-am maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-binPROGRAMS uninstall-libLTLIBRARIES \
	uninstall-nobase_dist_libucs_laHEADERS uninstall-pkgconfigDATA

.PRECIOUS: Makefile


all-local: $(objdir)/$(modulesubdir)

$(objdir)/$(modulesubdir): $(lib_LTLIBRARIES)
	$(AM_V_at)$(LN_RS) -fn $(localmoduledir) $(objdir)/$(modulesubdir)

#TODO	stats/stats_dump.c
#TODO	stats/stats_reader.c

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
