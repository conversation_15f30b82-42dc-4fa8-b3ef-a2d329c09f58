// Copyright (c) 2025 The LCI Project Authors
// SPDX-License-Identifier: MIT

#ifndef LCI_LCI_CONFIG_H
#define LCI_LCI_CONFIG_H

#define LCI_VERSION_MAJOR @LCI_VERSION_MAJOR@
#define LCI_VERSION_MINOR @LCI_VERSION_MINOR@
#define LCI_VERSION_PATCH @LCI_VERSION_PATCH@
#define LCI_VERSION_SUFFIX "@LCI_VERSION_SUFFIX@"
#define LCI_VERSION_HEX ((LCI_VERSION_MAJOR << 16) | \
                         (LCI_VERSION_MINOR << 8)  | \
                         (LCI_VERSION_PATCH))
#define LCI_VERSION_STRING "@LCI_VERSION@"
#define LCI_VERSION_FULL "@LCI_VERSION_FULL@"
#define LCI_BUILD_TIMESTAMP "@LCI_BUILD_TIMESTAMP@"

#cmakedefine LCI_USE_AVX

#cmakedefine LCI_DEBUG

#cmakedefine LCI_BACKEND_ENABLE_OFI
#cmakedefine LCI_BACKEND_ENABLE_IBV
#cmakedefine LCI_BACKEND_ENABLE_UCX
#cmakedefine LCI_NETWORK_BACKENDS_ENABLED "@LCI_NETWORK_BACKENDS_ENABLED@"

#cmakedefine LCI_USE_GPROF
#cmakedefine LCI_CONFIG_USE_ALIGNED_ALLOC
#cmakedefine LCI_USE_PERFORMANCE_COUNTER
#define LCI_OFI_PROVIDER_HINT_DEFAULT "@LCI_OFI_PROVIDER_HINT_DEFAULT@"
#cmakedefine LCI_USE_INLINE_CQ
#cmakedefine LCI_ENABLE_SLOWDOWN
#cmakedefine LCI_USE_PAPI
#cmakedefine01 LCI_USE_REG_CACHE_DEFAULT
#cmakedefine LCI_COMPILE_REG_CACHE

#define LCI_PACKET_SIZE_DEFAULT @LCI_PACKET_SIZE_DEFAULT@
#define LCI_BACKEND_MAX_SENDS_DEFAULT @LCI_BACKEND_MAX_SENDS_DEFAULT@
#define LCI_BACKEND_MAX_RECVS_DEFAULT @LCI_BACKEND_MAX_RECVS_DEFAULT@
#define LCI_BACKEND_MAX_CQES_DEFAULT @LCI_BACKEND_MAX_CQES_DEFAULT@
#define LCI_BACKEND_MAX_POLLS @LCI_BACKEND_MAX_POLLS@
#define LCI_PACKET_NUM_DEFAULT @LCI_PACKET_NUM_DEFAULT@
#define LCI_USE_MT_BACKEND_DEFAULT "@LCI_USE_MT_BACKEND_DEFAULT@"
#define LCI_CACHE_LINE      @LCI_CACHE_LINE@
#cmakedefine01 LCI_USE_IBV_TD_DEFAULT
#cmakedefine01 LCI_USE_PRG_ENDPOINT_DEFAULT
#define LCI_USE_RDV_PROTOCOL_DEFAULT "@LCI_USE_RDV_PROTOCOL_DEFAULT@"
#define LCI_USE_MAX_SINGLE_MESSAGE_SIZE_DEFAULT (@LCI_USE_MAX_SINGLE_MESSAGE_SIZE_DEFAULT@)

#define LCI_CQ_MAX_POLL 16
#define LCI_BACKEND_MAX_ENDPOINTS 8

#cmakedefine LCI_USE_CUDA

#endif  // LCI_LCI_CONFIG_H
