# Copyright (c) 2025 The LCI Project Authors
# SPDX-License-Identifier: MIT

add_lci_tests(
  TESTS
  all.cpp
  LABELS
  collective
  COMMANDS
  "${LCI_USE_CTEST_LAUNCHER} -n 2 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 3 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 4 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 5 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 6 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 7 ${LCI_USE_CTEST_ARGS} [TARGET]"
  "${LCI_USE_CTEST_LAUNCHER} -n 8 ${LCI_USE_CTEST_ARGS} [TARGET]"
  INCLUDES
  ${PROJECT_SOURCE_DIR}/tests/unit/include
  DEPENDENCIES
  gtest)
