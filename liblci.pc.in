prefix=@CMAKE_INSTALL_PREFIX@
exec_prefix=${prefix}
includedir=${prefix}/@CMAKE_INSTALL_INCLUDEDIR@
libdir=${exec_prefix}/@CMAKE_INSTALL_LIBDIR@

Name: liblct
Description: The Lightweight Communication Tools (LCT) library
Version: @CMAKE_PROJECT_VERSION@
Cflags: -I${includedir}
Libs: -L${libdir} -llct

Name: liblci
Description: The Lightweight Communication Interface (LCI) library
Version: @CMAKE_PROJECT_VERSION@
Requires.private: @PKGCONFIG_REQUIRES_PRIVATE@
Cflags: -I${includedir}
Libs: -L${libdir} -llct -llci-ucx -llci
Libs.private: @PKGCONFIG_LIBS_PRIVATE@
