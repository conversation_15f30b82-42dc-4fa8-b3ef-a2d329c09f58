# Generated binding layer
src/binding/generated/*

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Vscode related
.vscode/
.cache/
build/

# CLion related
.idea
cmake-build-*

# Spack related
spack-*

results
errors
*_test
build-*
vscode-build/
code_doc/
__pycache__
.fuse*
